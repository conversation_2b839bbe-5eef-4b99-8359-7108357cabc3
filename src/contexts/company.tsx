import { useQuery } from '@tanstack/react-query'
import { createContext, useContext, useMemo, type ReactNode } from 'react'

import { request, type APIResponse } from '@/lib/request'
import type { OrgTree } from '@/universal/types'

/**
 * Company context type definition
 */
export interface CompanyContextType {
  /** Array of organization trees */
  companyTree: OrgTree[]
}

const CompanyContext = createContext<CompanyContextType | undefined>(undefined)

/**
 * Company provider component that fetches and provides organization data
 */
export function CompanyProvider({
  children,
}: Readonly<{ children: ReactNode }>) {
  const { data: companyTree } = useQuery({
    queryKey: ['orgs', 'tree'],
    queryFn: async () => {
      const response = await request<APIResponse<OrgTree[]>>('/orgs/tree')
      if (response.code !== 200001) {
        throw new Error(response.message || '获取组织架构失败')
      }
      return response.data || []
    },
    staleTime: 30 * 60 * 1000, // 30 minutes cache
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  })

  // Memoized context value to prevent unnecessary re-renders
  const contextValue = useMemo<CompanyContextType>(
    () => ({
      companyTree: companyTree || [],
    }),
    [companyTree],
  )

  return (
    <CompanyContext.Provider value={contextValue}>
      {children}
    </CompanyContext.Provider>
  )
}

/**
 * Hook to access company context
 * @returns CompanyContextType
 * @throws Error if used outside CompanyProvider
 */
// eslint-disable-next-line react-refresh/only-export-components
export function useCompany(): CompanyContextType {
  const context = useContext(CompanyContext)
  if (!context) {
    throw new Error('useCompany must be used within a CompanyProvider')
  }
  return context
}
