import { useQuery } from '@tanstack/react-query'
import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import { createContext, useContext, useState, type ReactNode } from 'react'

import { request, type APIResponse } from '@/lib/request'

interface AppContextType {
  date: Dayjs
  setDate: (date: Dayjs) => void
  currentDate: CurrentDate | null
}

interface CurrentDate {
  current_time: string
  month: string
  month_time: string
  quarter: string
  quarter_time: string
  year: string
}

const AppContext = createContext<AppContextType | undefined>(undefined)

export function AppProvider({ children }: { children: ReactNode }) {
  const [date, setDate] = useState<Dayjs>(dayjs())

  const { data: CurrentDate } = useQuery<CurrentDate | null>({
    queryKey: ['get', '/base/current-time'],
    queryFn: async ({ queryKey: [, url] }) => {
      const response = await request<APIResponse<CurrentDate>>(url as string)
      if (response.code !== 200001) return null
      return response.data
    },
    staleTime: 0,
    retry: false,
  })

  const value: AppContextType = {
    date,
    setDate,
    currentDate: CurrentDate ?? null,
  }
  return <AppContext.Provider value={value}>{children}</AppContext.Provider>
}

// eslint-disable-next-line react-refresh/only-export-components
export function useApp(): AppContextType {
  const context = useContext(AppContext)
  if (!context) {
    throw new Error('useApp must be used within an AppProvider')
  }
  return context
}
