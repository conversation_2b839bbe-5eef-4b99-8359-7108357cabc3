import { useQuery } from '@tanstack/react-query'
import * as React from 'react'

import { getLogoutURL, removeToken } from '@/lib/auth'
import { request, type APIResponse } from '@/lib/request'

export interface UserProfile {
  id: string
  username: string
  avatar: string
  role: string[]
  mobile: string
  company: string
  company_id: string
}

export interface CompanyInfo {
  id: string
  name: string // 组织名称
  parent_id: string // 上级组织ID
  type: string // 组织类型
  level: number // 层级 1集团 2二级 3三级
  order: number // 排序
  approval_id: string // 审批组织ID
  code: string // 组织编码
  uc_id: string // 用户中心ID
  enabled: boolean // 是否启用
}

export interface AuthContext {
  logout: () => Promise<void>
  user: UserProfile | null
  company: CompanyInfo | null
}

const AuthContext = React.createContext<AuthContext | null>(null)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const { data: user } = useQuery<UserProfile | null>({
    queryKey: ['get', '/user-info'],
    queryFn: async ({ queryKey: [, url] }) => {
      const response = await request<APIResponse<UserProfile>>(url as string)
      if (response.code !== 200001) return null
      return response.data
    },
    staleTime: 0,
    retry: false,
  })

  const { data: company } = useQuery<CompanyInfo | null>({
    queryKey: ['get', '/orgs/detial-by-user'],
    queryFn: async ({ queryKey: [, url] }) => {
      const response = await request<APIResponse<CompanyInfo>>(url as string)
      if (response.code !== 200001) return null
      return response.data
    },
    staleTime: 0,
    retry: false,
  })

  const logout = React.useCallback(async () => {
    removeToken()
    window.location.href = getLogoutURL()
  }, [])
  return (
    <AuthContext.Provider
      value={{ user: user ?? null, company: company ?? null, logout }}
    >
      {children}
    </AuthContext.Provider>
  )
}

// eslint-disable-next-line react-refresh/only-export-components
export function useAuth() {
  const context = React.useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
