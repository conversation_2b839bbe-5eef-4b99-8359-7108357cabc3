import { useMutation, useQuery } from '@tanstack/react-query'
import { useNavigate, useParams } from '@tanstack/react-router'
import {
  Button,
  Cascader,
  DatePicker,
  Divider,
  Form,
  Input,
  message,
  Radio,
  Select,
  Spin,
} from 'antd'
import dayjs from 'dayjs'
import { useCallback, useState } from 'react'

import { useAuth } from '@/contexts/auth.tsx'
import { type APIResponse, request } from '@/lib/request.ts'
import {
  getIndustryNewTypePath,
  getIndustryNewTypeValue,
} from '@/universal/basic-form'
import {
  HAS_EQUIPMENT_UPGRADE,
  INDUSTRY_NEW_TYPE,
  INDUSTRY_TYPE,
  INVESTOR_TYPE_OPTIONS,
  OVERSEA_PROJECT_AREA,
  PROJECT_AREA,
  PROJECT_CATEGORY,
  SPECIAL_CONSIDERATION_TYPE,
} from '@/universal/basic-form/constants.ts'
import type {
  FixedAssetDTO,
  FixedAssetForm,
} from '@/universal/basic-form/types.ts'

export function FixedAssetForm({ isUpdate }: Readonly<{ isUpdate?: boolean }>) {
  const { user } = useAuth()
  const navigate = useNavigate()
  const { id: approvalNodeId } = useParams({ strict: false })

  const [form] = Form.useForm()

  const [lockData, setLockData] = useState<boolean>(false)

  const getFormData = useQuery({
    queryKey: [approvalNodeId],
    queryFn: async ({ queryKey: [id] }) => {
      const res = await request<APIResponse<FixedAssetDTO>>(
        `/fixed-assets/detail-by-approval-node-id?approval_node_id=${id}`,
      )

      if (res.code === 200001) {
        const {
          actual_start_date,
          complete_time_expect,
          industry_new_type,
          investment_year,
          lock_data,
          project_category,
          start_time,
          ...data
        } = res.data

        setLockData(lock_data === 1)

        const formData = {
          ...data,
          actual_start_date: dayjs(actual_start_date),
          complete_time_expect: dayjs(complete_time_expect),
          industry_new_type: getIndustryNewTypePath(industry_new_type),
          investment_year: dayjs(investment_year),
          project_category: project_category.split(','),
          start_time: dayjs(start_time),
        }

        if (lock_data === 1) {
          const disabledFields = [
            'company_id',
            'company_name',
            'investor_type',
            'project_name',
            'project_area',
            'project_content',
            'project_expect',
            'industry_type',
            'is_plan_investment',
            'is_real_estate_investment',
            'project_plan_total_investment',
            'investmentcompleted_last_year',
            'plan_investment_cur_year',
            'investment_return_rate_expect',
            'has_equipment_upgrade',
            'equipment_upgrade_amount',
            'is_key_project',
            'project_phase',
            'special_consideration_type',
            'special_total_investment',
            'investment_necessity',
            'fund_source_self',
            'fund_source_loan',
            'fund_source_other',
            'remarks',
            'project_progress_desc',
            'investment_completed_cur_year',
            'completed_fund_source_self',
            'completed_fund_source_loan',
            'completed_fund_source_other',
            'funds_received',
            'completed_remarks',
            'actual_start_date',
            'complete_time_expect',
            'industry_new_type',
            'investment_year',
            'project_category',
            'start_time',
          ]

          form.setFields(
            disabledFields.map((item) => ({
              name: item,
              disabled: true,
            })),
          )
        }

        form.setFieldsValue(formData)

        return formData
      }
      message.error(res.message)
    },
    enabled: !!approvalNodeId,
    refetchOnWindowFocus: false,
  })

  const onSubmit = useMutation({
    mutationFn: async (values: FixedAssetForm) => {
      const {
        actual_start_date,
        complete_time_expect,
        industry_new_type,
        investment_year,
        project_category,
        start_time,
        ...data
      } = values

      const bodyData = {
        ...data,
        actual_start_date: dayjs(actual_start_date).format('YYYY-MM-DD'),
        complete_time_expect: dayjs(complete_time_expect).format('YYYY-MM-DD'),
        industry_new_type: getIndustryNewTypeValue(
          industry_new_type as unknown as string[],
        ),
        investment_year: dayjs(investment_year).format('YYYY'),
        project_category: Array.isArray(project_category)
          ? project_category.join(',')
          : project_category,
        start_time: dayjs(start_time).format('YYYY-MM-DD'),
      }

      let res

      if (isUpdate) {
        res = await request<APIResponse<Record<string, string>>>(
          '/fixed-assets/modify',
          {
            method: 'PUT',
            body: { approval_node_id: approvalNodeId, ...bodyData },
          },
        )
      } else {
        res = await request<APIResponse<Record<string, string>>>(
          '/fixed-assets/create',
          { method: 'POST', body: bodyData },
        )
      }

      if (res.code === 200001) {
        message.success('操作成功')
        await navigate({ to: '/basic-report/fixed-assets' })
        return
      }
      message.error(res?.message)
    },
    onError: (err) => message.error(JSON.stringify(err)),
  })

  const watchInvestorType = Form.useWatch('investor_type', form)
  const watchHasEquipmentUpgrade = Form.useWatch('has_equipment_upgrade', form)

  const handleValuesChange = useCallback(
    (values: { [key: string]: unknown }) => {
      if ('investor_type' in values) {
        form.setFieldsValue({
          project_area: '',
        })
      }
    },
    [form],
  )

  return (
    <Spin spinning={onSubmit.isPending || getFormData.isFetching}>
      <Form
        form={form}
        labelCol={{ style: { width: '160px' } }}
        labelAlign="left"
        scrollToFirstError={{
          block: 'center',
          behavior: 'smooth',
        }}
        onValuesChange={handleValuesChange}
        onFinish={onSubmit.mutate}
      >
        <div className="flex flex-col gap-6">
          <h4 className="text-sm font-semibold text-[#266EFF]">项目基础信息</h4>
          <div>
            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="填报年份"
                name="investment_year"
                rules={[{ required: true, message: '请输入填报年份' }]}
                initialValue={dayjs()}
              >
                <DatePicker
                  picker="year"
                  className="w-full"
                  placeholder="请选择填报年份"
                />
              </Form.Item>
              <Form.Item
                label="编制单位"
                name="company_id"
                rules={[{ required: true, message: '请选择编制单位' }]}
              >
                <Select
                  className="w-full"
                  placeholder="请选择编制单位"
                  options={
                    isUpdate
                      ? [
                          {
                            label: form.getFieldValue('company_name'),
                            value: form.getFieldValue('company_id'),
                          },
                        ]
                      : [{ label: user?.company, value: user?.company_id }]
                  }
                  onChange={(_, option) => {
                    if (Array.isArray(option)) {
                      form.setFieldValue('company_name', option[0]?.label)
                    } else {
                      form.setFieldValue('company_name', option?.label)
                    }
                  }}
                />
              </Form.Item>
              <Form.Item name="company_name" noStyle></Form.Item>
            </div>
            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="投资分类"
                name="investor_type"
                rules={[{ required: true, message: '请选择投资分类' }]}
              >
                <Select
                  className="w-full"
                  placeholder="请选择投资分类"
                  options={INVESTOR_TYPE_OPTIONS}
                />
              </Form.Item>
              <Form.Item
                label="项目名称"
                name="project_name"
                rules={[{ required: true, message: '请输入项目名称' }]}
              >
                <Input className="w-full" placeholder="请输入项目名称" />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="项目地点"
                name="project_area"
                rules={[{ required: true, message: '请选择项目地点' }]}
              >
                {watchInvestorType === 1 || watchInvestorType === 2 ? (
                  <Select
                    className="w-full"
                    placeholder="请选择项目地点"
                    options={PROJECT_AREA.map((item) => ({
                      label: item,
                      value: item,
                    }))}
                  />
                ) : (
                  <Select
                    className="w-full"
                    placeholder="请选择项目地点"
                    popupRender={(menu) => (
                      <>
                        {menu}
                        <Divider style={{ margin: '8px 0' }} />
                        <Input
                          placeholder="输入新地点"
                          onChange={(e) =>
                            form.setFieldValue('project_area', e.target.value)
                          }
                          onKeyDown={(e) => e.stopPropagation()}
                        />
                      </>
                    )}
                    options={OVERSEA_PROJECT_AREA.map((item) => ({
                      label: item,
                      value: item,
                    }))}
                  />
                )}
              </Form.Item>
              <Form.Item
                label="项目分类"
                name="project_category"
                rules={[{ required: true, message: '请选择项目分类' }]}
              >
                <Select
                  allowClear
                  className="w-full"
                  mode="multiple"
                  options={PROJECT_CATEGORY}
                  optionFilterProp="label"
                  placeholder="请选择项目分类"
                  labelRender={({ value }) => value}
                  onSearch={(value) =>
                    PROJECT_CATEGORY.filter((item) =>
                      item.label.includes(value),
                    )
                  }
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1">
              <Form.Item
                label="项目内容"
                name="project_content"
                rules={[{ required: true, message: '请输入项目内容' }]}
              >
                <Input.TextArea
                  autoSize={{ minRows: 2, maxRows: 6 }}
                  className="w-full"
                  placeholder="请输入项目内容"
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1">
              <Form.Item
                name="project_expect"
                label={
                  <span className="h-12">
                    预期实现效果、主
                    <br />
                    要风险及应对举措
                  </span>
                }
                rules={[
                  {
                    required: true,
                    message: '请输入预期实现效果、主要风险及应对举措',
                  },
                ]}
              >
                <Input.TextArea
                  autoSize={{ minRows: 2, maxRows: 6 }}
                  className="w-full"
                  placeholder="请输入预期实现效果、主要风险及应对举措"
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="所属行业"
                name="industry_type"
                rules={[{ required: true, message: '请选择所属行业' }]}
              >
                <Select
                  allowClear
                  className="w-full"
                  labelRender={({ label, value }) =>
                    `${label as string}${value}`
                  }
                  onSearch={(value) =>
                    INDUSTRY_TYPE.filter((item) => item.label.includes(value))
                  }
                  optionFilterProp="label"
                  options={INDUSTRY_TYPE}
                  placeholder="请选择所属行业"
                  showSearch
                />
              </Form.Item>
              <Form.Item label="所属战新产业" name="industry_new_type">
                <Cascader
                  className="w-full"
                  placeholder="请选择所属战新产业"
                  options={INDUSTRY_NEW_TYPE}
                  showSearch
                  allowClear
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="是否计划内投资"
                name="is_plan_investment"
                rules={[{ required: true, message: '请选择是否计划内投资' }]}
              >
                <Radio.Group>
                  <Radio value={1}>是</Radio>
                  <Radio value={0}>否</Radio>
                </Radio.Group>
              </Form.Item>
              <Form.Item
                label="是否为房地产投资"
                name="is_real_estate_investment"
                rules={[{ required: true, message: '请选择是否为房地产投资' }]}
              >
                <Radio.Group>
                  <Radio value={1}>是</Radio>
                  <Radio value={0}>否</Radio>
                </Radio.Group>
              </Form.Item>
            </div>
            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="项目计划总投资"
                name="project_plan_total_investment"
                rules={[{ required: true, message: '请输入项目计划总投资' }]}
                getValueFromEvent={(e) =>
                  e.target.value === '' ? null : Number(e.target.value)
                }
              >
                <Input
                  className="w-full"
                  placeholder="输入项目计划总投资"
                  suffix="万元"
                  type="number"
                />
              </Form.Item>
              <Form.Item
                label="截止上年底完成投资"
                name="investmentcompleted_last_year"
                rules={[
                  { required: true, message: '请输入截止上年底完成投资' },
                ]}
                getValueFromEvent={(e) =>
                  e.target.value === '' ? null : Number(e.target.value)
                }
              >
                <Input
                  className="w-full"
                  placeholder="输入截止上年底完成投资"
                  suffix="万元"
                  type="number"
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="本年计划投资"
                name="plan_investment_cur_year"
                dependencies={[
                  'fund_source_self',
                  'fund_source_loan',
                  'fund_source_other',
                ]}
                rules={[
                  { required: true, message: '请输入本年计划投资' },
                  {
                    validator: (_, value) => {
                      const current = Number(value ?? 0)
                      const total =
                        Number(form.getFieldValue('fund_source_self') ?? 0) +
                        Number(form.getFieldValue('fund_source_loan') ?? 0) +
                        Number(form.getFieldValue('fund_source_other') ?? 0)
                      if (current !== total) {
                        return Promise.reject(
                          new Error(
                            '三项资金来源之和不等于本年计划投资额，请确认！',
                          ),
                        )
                      }
                      return Promise.resolve()
                    },
                  },
                ]}
                getValueFromEvent={(e) =>
                  e.target.value === '' ? null : Number(e.target.value)
                }
              >
                <Input
                  className="w-full"
                  placeholder="输入本年计划投资"
                  suffix="万元"
                  type="number"
                />
              </Form.Item>
              <Form.Item
                label="项目起始时间"
                name="start_time"
                rules={[{ required: true, message: '请选择项目起始时间' }]}
              >
                <DatePicker
                  className="w-full"
                  placeholder="请选择项目起始时间"
                  onChange={(value) => dayjs(value).format('YYYY - MM - DD')}
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="项目预计完成时间"
                name="complete_time_expect"
                rules={[{ required: true, message: '请选择项目预计完成时间' }]}
              >
                <DatePicker
                  className="w-full"
                  placeholder="请选择项目预计完成时间"
                />
              </Form.Item>
              <Form.Item
                label="预计投资收益率"
                name="investment_return_rate_expect"
                rules={[{ required: true, message: '请输入预计投资收益率' }]}
                getValueFromEvent={(e) =>
                  e.target.value === '' ? null : Number(e.target.value)
                }
              >
                <Input
                  className="w-full"
                  placeholder="输入预计投资收益率"
                  suffix="%"
                  type="number"
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="是否涉及设备更新"
                name="has_equipment_upgrade"
                rules={[{ required: true, message: '请选择是否涉及设备更新' }]}
              >
                <Select
                  className="w-full"
                  placeholder="请选择是否涉及设备更新"
                  options={HAS_EQUIPMENT_UPGRADE}
                />
              </Form.Item>
              <Form.Item
                label="设备更新数额"
                name="equipment_upgrade_amount"
                rules={[
                  {
                    required: watchHasEquipmentUpgrade !== 1,
                    message: '请输入设备更新数额',
                  },
                ]}
                getValueFromEvent={(e) =>
                  e.target.value === '' ? null : Number(e.target.value)
                }
              >
                <Input
                  className="w-full"
                  placeholder="输入设备更新数额"
                  suffix="万元"
                  type="number"
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="是否重点项目"
                name="is_key_project"
                rules={[
                  {
                    required: watchHasEquipmentUpgrade !== 1,
                    message: '请选择是否重点项目',
                  },
                ]}
              >
                <Radio.Group>
                  <Radio value={1}>是</Radio>
                  <Radio value={0}>否</Radio>
                </Radio.Group>
              </Form.Item>
              <Form.Item
                label="项目阶段"
                name="project_phase"
                rules={[{ required: true, message: '请选择项目阶段' }]}
              >
                <Radio.Group>
                  <Radio value={1}>新开工</Radio>
                  <Radio value={2}>续建</Radio>
                </Radio.Group>
              </Form.Item>
            </div>
            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="特殊事项考虑类型"
                name="special_consideration_type"
                rules={[
                  {
                    required:
                      watchInvestorType === 2 || watchInvestorType === 4,
                    message: '请选择特殊事项考虑类型',
                  },
                ]}
              >
                <Select
                  className="w-full"
                  placeholder="请选择特殊事项考虑类型"
                  options={SPECIAL_CONSIDERATION_TYPE}
                  allowClear
                />
              </Form.Item>
              <Form.Item
                label="特殊事项对应投资额"
                name="special_total_investment"
                rules={[
                  {
                    required:
                      watchInvestorType === 2 || watchInvestorType === 4,
                    message: '请输入特殊事项对应投资额',
                  },
                ]}
                getValueFromEvent={(e) =>
                  e.target.value === '' ? null : Number(e.target.value)
                }
              >
                <Input
                  className="w-full"
                  placeholder="输入特殊事项对应投资额"
                  suffix="万元"
                  type="number"
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1">
              <Form.Item
                label="投资目的及必要性"
                name="investment_necessity"
                rules={[
                  {
                    required:
                      watchInvestorType === 2 || watchInvestorType === 4,
                    message: '请输入投资目的及必要性',
                  },
                ]}
              >
                <Input.TextArea
                  className="w-full"
                  placeholder="输入投资目的及必要性"
                  autoSize={{ minRows: 2, maxRows: 4 }}
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="资金来源-自有资金"
                name="fund_source_self"
                rules={[{ required: true, message: '请输入资金来源-自有资金' }]}
                getValueFromEvent={(e) =>
                  e.target.value === '' ? null : Number(e.target.value)
                }
              >
                <Input
                  className="w-full"
                  placeholder="请输入资金来源-自有资金"
                  type="number"
                  suffix="万元"
                />
              </Form.Item>
              <Form.Item
                label="资金来源-贷款"
                name="fund_source_loan"
                rules={[{ required: true, message: '请输入资金来源-贷款' }]}
                getValueFromEvent={(e) =>
                  e.target.value === '' ? null : Number(e.target.value)
                }
              >
                <Input
                  className="w-full"
                  placeholder="输入资金来源-贷款"
                  suffix="万元"
                  type="number"
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="资金来源-其他"
                name="fund_source_other"
                rules={[{ required: true, message: '请输入资金来源-其他' }]}
                getValueFromEvent={(e) =>
                  e.target.value === '' ? null : Number(e.target.value)
                }
              >
                <Input
                  className="w-full"
                  placeholder="请输入资金来源-其他"
                  type="number"
                  suffix="万元"
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1">
              <Form.Item label="备注" name="remarks">
                <Input.TextArea
                  className="w-full"
                  placeholder="输入备注"
                  autoSize={{ minRows: 2, maxRows: 4 }}
                />
              </Form.Item>
            </div>
          </div>
          <h4 className="text-sm font-semibold text-[#266EFF]">项目完成情况</h4>
          <div>
            <div className="grid grid-cols-1">
              <Form.Item
                label="项目进度描述"
                name="project_progress_desc"
                rules={[{ required: isUpdate, message: '请输入项目进度描述' }]}
              >
                <Input.TextArea
                  className="w-full"
                  placeholder="输入项目进度描述"
                  autoSize={{ minRows: 2, maxRows: 4 }}
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="本年度完成投资"
                name="investment_completed_cur_year"
                dependencies={[
                  'completed_fund_source_self',
                  'completed_fund_source_loan',
                  'completed_fund_source_other',
                ]}
                rules={[
                  { required: isUpdate, message: '请输入本年度完成投资' },
                  {
                    validator: (_, value) => {
                      const current = Number(value ?? 0)
                      const prev = Number(
                        getFormData.data?.investment_completed_cur_year ?? 0,
                      )
                      if (current < prev) {
                        return Promise.reject(
                          new Error(
                            `完成投资额不得小于上一次上报的值: ${prev}`,
                          ),
                        )
                      }

                      const s1 = Number(
                        form.getFieldValue('completed_fund_source_self') ?? 0,
                      )
                      const s2 = Number(
                        form.getFieldValue('completed_fund_source_loan') ?? 0,
                      )
                      const s3 = Number(
                        form.getFieldValue('completed_fund_source_other') ?? 0,
                      )

                      if (current !== s1 + s2 + s3) {
                        return Promise.reject(
                          new Error(
                            '三项完成投资额之和不等于本年实际投资额，请确认！',
                          ),
                        )
                      }
                      return Promise.resolve()
                    },
                  },
                ]}
                getValueFromEvent={(e) =>
                  e.target.value === '' ? null : Number(e.target.value)
                }
              >
                <Input
                  className="w-full"
                  placeholder="输入本年度完成投资"
                  suffix="万元"
                  type="number"
                />
              </Form.Item>
              <Form.Item
                label="完成投资额-自有资金"
                name="completed_fund_source_self"
                rules={[
                  { required: isUpdate, message: '请输入完成投资额-自有资金' },
                ]}
                getValueFromEvent={(e) =>
                  e.target.value === '' ? null : Number(e.target.value)
                }
              >
                <Input
                  className="w-full"
                  placeholder="输入完成投资额-自有资金"
                  suffix="万元"
                  type="number"
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="完成投资额-贷款"
                name="completed_fund_source_loan"
                rules={[
                  { required: isUpdate, message: '请输入完成投资额-贷款' },
                ]}
                getValueFromEvent={(e) =>
                  e.target.value === '' ? null : Number(e.target.value)
                }
              >
                <Input
                  className="w-full"
                  placeholder="输入完成投资额-贷款"
                  suffix="万元"
                  type="number"
                />
              </Form.Item>
              <Form.Item
                label="完成投资额-其他"
                name="completed_fund_source_other"
                rules={[
                  { required: isUpdate, message: '请输入完成投资额-其他' },
                ]}
                getValueFromEvent={(e) =>
                  e.target.value === '' ? null : Number(e.target.value)
                }
              >
                <Input
                  className="w-full"
                  placeholder="输入完成投资额-其他"
                  suffix="万元"
                  type="number"
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="实际开工年月"
                name="actual_start_date"
                rules={[{ required: isUpdate, message: '请选择实际开工年月' }]}
              >
                <DatePicker
                  className="w-full"
                  placeholder="请选择实际开工年月"
                />
              </Form.Item>
              <Form.Item
                label="到位资金"
                name="funds_received"
                rules={[{ required: isUpdate, message: '请输入到位资金' }]}
                getValueFromEvent={(e) =>
                  e.target.value === '' ? null : Number(e.target.value)
                }
              >
                <Input
                  className="w-full"
                  placeholder="输入到位资金"
                  suffix="万元"
                  type="number"
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1">
              <Form.Item label="备注" name="completed_remarks">
                <Input.TextArea
                  className="w-full"
                  placeholder="输入备注"
                  autoSize={{ minRows: 2, maxRows: 4 }}
                />
              </Form.Item>
            </div>
          </div>
        </div>
        <div className="sticky bottom-0 flex justify-end gap-2 bg-white py-2">
          <Button type="primary" htmlType="submit">
            {isUpdate ? '更新数据' : '保存数据'}
          </Button>
          <Button
            onClick={() => navigate({ to: '/basic-report/fixed-assets' })}
          >
            取消
          </Button>
        </div>
      </Form>
    </Spin>
  )
}
