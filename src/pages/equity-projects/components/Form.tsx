import { useMutation, useQuery } from '@tanstack/react-query'
import { useNavigate, useParams } from '@tanstack/react-router'
import {
  Button,
  Cascader,
  DatePicker,
  Divider,
  Form,
  Input,
  message,
  Radio,
  Select,
  Spin,
} from 'antd'
import dayjs from 'dayjs'

import { useAuth } from '@/contexts/auth.tsx'
import { type APIResponse, request } from '@/lib/request.ts'
import {
  getIndustryNewTypePath,
  getIndustryNewTypeValue,
} from '@/universal/basic-form'
import {
  EQUITY_INVESTMENT_TYPE,
  INDUSTRY_NEW_TYPE,
  INDUSTRY_TYPE,
  INVESTOR_TYPE_OPTIONS,
  OVERSEA_PROJECT_AREA,
  PROJECT_AREA,
  PROJECT_CATEGORY,
  SPECIAL_CONSIDERATION_TYPE,
} from '@/universal/basic-form/constants.ts'
import type {
  EquityProjectDTO,
  EquityProjectForm,
} from '@/universal/basic-form/types.ts'

export function EquityProjectForm({
  isUpdate,
}: Readonly<{ isUpdate?: boolean }>) {
  const { user } = useAuth()

  const navigate = useNavigate()
  const params = useParams({ strict: false })

  const [form] = Form.useForm()

  const getFormData = useQuery({
    queryKey: ['id', params.id],
    queryFn: async ({ queryKey: [, id] }) => {
      const res = await request<APIResponse<EquityProjectDTO>>(
        `/equity-investment/detail-by-approval-node-id?approval_node_id=${id}`,
      )

      if (res.code === 200001) {
        const {
          complete_time_expect,
          industry_new_type,
          investment_year,
          project_category,
          start_time,
          ...data
        } = res.data

        const formData = {
          ...data,
          complete_time_expect: dayjs(complete_time_expect),
          industry_new_type: getIndustryNewTypePath(industry_new_type),
          investment_year: dayjs(investment_year),
          project_category: project_category?.split(','),
          start_time: dayjs(start_time),
        }

        form.setFieldsValue(formData)
        return formData
      }
      message.error(res.message)
    },
    enabled: !!params.id,
    refetchOnWindowFocus: false,
  })

  const submitForm = useMutation({
    mutationFn: async (values: EquityProjectForm) => {
      const {
        complete_time_expect,
        industry_new_type,
        investment_year,
        project_category,
        start_time,
        ...data
      } = values

      const bodyData = {
        ...data,
        complete_time_expect: dayjs(complete_time_expect).format('YYYY-MM-DD'),
        industry_new_type: getIndustryNewTypeValue(
          industry_new_type as unknown as string[],
        ),
        investment_year: dayjs(investment_year).format('YYYY'),
        project_category: Array.isArray(project_category)
          ? project_category.join(',')
          : project_category,
        start_time: dayjs(start_time).format('YYYY-MM-DD'),
      }

      if (isUpdate) {
        const res = await request<APIResponse<Record<string, string>>>(
          '/equity-investment/modify',
          { method: 'PUT', body: { approval_node_id: params.id, ...bodyData } },
        )
        if (res.code === 200001) {
          message.success('操作成功')
          await navigate({ to: '/basic-report/equity-projects' })
          return
        }
        message.error(res?.message)

        return
      }

      const res = await request<APIResponse<Record<string, string>>>(
        '/equity-investment/create',
        { method: 'POST', body: bodyData },
      )
      if (res.code === 200001) {
        message.success('操作成功')
        await navigate({ to: '/basic-report/equity-projects' })
        return
      }
      message.error(res?.message)
    },
    onError: (err) => message.error(JSON.stringify(err)),
  })

  const watchInvestorType = Form.useWatch('investor_type', form)
  const watchIndustryNewType = Form.useWatch('industry_new_type', form)

  return (
    <Spin spinning={submitForm.isPending || getFormData.isFetching}>
      <Form
        form={form}
        labelCol={{ style: { width: '160px' } }}
        labelAlign="left"
        scrollToFirstError={{
          block: 'center',
          behavior: 'smooth',
        }}
        onFinish={submitForm.mutate}
      >
        <div className="flex flex-col gap-6">
          <h4 className="text-sm font-semibold text-[#266EFF]">项目基础信息</h4>
          <div>
            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="填报年份"
                name="investment_year"
                rules={[{ required: true, message: '请输入填报年份' }]}
                initialValue={dayjs()}
              >
                <DatePicker
                  picker="year"
                  className="w-full"
                  placeholder="请选择填报年份"
                  format="YYYY"
                />
              </Form.Item>
              <Form.Item
                label="编制单位"
                name="company_id"
                rules={[{ required: true, message: '请选择编制单位' }]}
              >
                <Select
                  className="w-full"
                  placeholder="请选择编制单位"
                  options={[{ label: user?.company, value: user?.company_id }]}
                  onChange={(_, option) => {
                    if (Array.isArray(option)) {
                      form.setFieldValue('company_name', option[0]?.label)
                    } else {
                      form.setFieldValue('company_name', option?.label)
                    }
                  }}
                />
              </Form.Item>
              <Form.Item name="company_name" noStyle></Form.Item>
            </div>
            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="投资分类"
                name="investor_type"
                rules={[{ required: true, message: '请选择投资分类' }]}
              >
                <Select
                  className="w-full"
                  placeholder="请选择投资分类"
                  options={INVESTOR_TYPE_OPTIONS}
                />
              </Form.Item>
              <Form.Item
                label="项目名称"
                name="project_name"
                rules={[{ required: true, message: '请输入项目名称' }]}
              >
                <Input className="w-full" placeholder="请输入项目名称" />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="项目地点"
                name="project_area"
                rules={[{ required: true, message: '请选择项目地点' }]}
              >
                {watchInvestorType === 1 || watchInvestorType === 2 ? (
                  <Select
                    className="w-full"
                    placeholder="请选择项目地点"
                    options={PROJECT_AREA.map((item) => ({
                      label: item,
                      value: item,
                    }))}
                    showSearch
                  />
                ) : (
                  <Select
                    className="w-full"
                    placeholder="请选择项目地点"
                    popupRender={(menu) => (
                      <>
                        {menu}
                        <Divider style={{ margin: '8px 0' }} />
                        <Input
                          placeholder="输入新地点"
                          onChange={(e) =>
                            form.setFieldValue('project_area', e.target.value)
                          }
                          onKeyDown={(e) => e.stopPropagation()}
                        />
                      </>
                    )}
                    options={OVERSEA_PROJECT_AREA.map((item) => ({
                      label: item,
                      value: item,
                    }))}
                  />
                )}
              </Form.Item>
              <Form.Item
                label="项目分类"
                name="project_category"
                rules={[{ required: true, message: '请选择项目分类' }]}
              >
                <Select
                  allowClear
                  className="w-full"
                  mode="multiple"
                  options={PROJECT_CATEGORY}
                  optionFilterProp="label"
                  placeholder="请选择项目分类"
                  labelRender={({ value }) => value}
                  onSearch={(value) =>
                    PROJECT_CATEGORY.filter((item) =>
                      item.label.includes(value),
                    )
                  }
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1">
              <Form.Item
                label="项目内容"
                name="project_content"
                rules={[{ required: true, message: '请输入项目内容' }]}
              >
                <Input.TextArea
                  autoSize={{ minRows: 2, maxRows: 6 }}
                  className="w-full"
                  placeholder="请输入项目内容"
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1">
              <Form.Item
                label={
                  <span className="h-12">
                    预期实现效果、主
                    <br />
                    要风险及应对举措
                  </span>
                }
                name="project_expect"
                rules={[
                  {
                    required: true,
                    message: '请输入预期实现效果、主要风险及应对举措',
                  },
                ]}
              >
                <Input.TextArea
                  autoSize={{ minRows: 2, maxRows: 6 }}
                  className="w-full"
                  placeholder="战略类和发展类项目重点填写项目投产后预期对促进产业发展、实现战略布局等方面实现的效果，关注类和管控类项目重点填写面临的主要风险及应对举措。"
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="所属行业"
                name="industry_type"
                rules={[{ required: true, message: '请选择所属行业' }]}
              >
                <Select
                  allowClear
                  className="w-full"
                  labelRender={({ label, value }) =>
                    `${label as string}${value}`
                  }
                  onSearch={(value) =>
                    INDUSTRY_TYPE.filter((item) => item.label.includes(value))
                  }
                  optionFilterProp="label"
                  options={INDUSTRY_TYPE}
                  placeholder="请选择所属行业"
                  showSearch
                />
              </Form.Item>
              <Form.Item label="所属战新行业" name="industry_new_type">
                <Cascader
                  className="w-full"
                  placeholder="请选择所属战新产业"
                  options={INDUSTRY_NEW_TYPE}
                  showSearch
                  allowClear
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="股权投资类型"
                name="equity_investment_type"
                rules={[
                  {
                    required: !!watchIndustryNewType,
                    message: '请选择股权投资类型',
                  },
                ]}
              >
                <Select
                  className="w-full"
                  placeholder="请选择股权投资类型"
                  options={EQUITY_INVESTMENT_TYPE}
                />
              </Form.Item>
              <Form.Item
                label="是否计划内投资"
                name="is_plan_investment"
                rules={[{ required: true, message: '请选择是否计划内投资' }]}
              >
                <Radio.Group>
                  <Radio value={1}>是</Radio>
                  <Radio value={0}>否</Radio>
                </Radio.Group>
              </Form.Item>
            </div>
            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="项目计划总投资"
                name="project_plan_total_investment"
                rules={[{ required: true, message: '请输入项目计划总投资' }]}
                getValueFromEvent={(e) =>
                  e.target.value === '' ? null : Number(e.target.value)
                }
              >
                <Input
                  className="w-full"
                  placeholder="输入项目计划总投资"
                  suffix="万元"
                  type="number"
                />
              </Form.Item>
              <Form.Item
                label="截止上年底完成投资"
                name="investmentcompleted_last_year"
                rules={[
                  { required: true, message: '请输入截止上年底完成投资' },
                ]}
                getValueFromEvent={(e) =>
                  e.target.value === '' ? null : Number(e.target.value)
                }
              >
                <Input
                  className="w-full"
                  placeholder="输入截止上年底完成投资"
                  suffix="万元"
                  type="number"
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="本年计划投资"
                name="plan_investment_cur_year"
                dependencies={[
                  'fund_source_self',
                  'fund_source_loan',
                  'fund_source_other',
                ]}
                rules={[
                  { required: true, message: '请输入本年计划投资' },
                  {
                    validator: (_, value) => {
                      const current = Number(value ?? 0)
                      const total =
                        Number(form.getFieldValue('fund_source_self') ?? 0) +
                        Number(form.getFieldValue('fund_source_loan') ?? 0) +
                        Number(form.getFieldValue('fund_source_other') ?? 0)
                      if (current !== total) {
                        return Promise.reject(
                          new Error(
                            '三项资金来源之和不等于本年计划投资额，请确认！',
                          ),
                        )
                      }
                      return Promise.resolve()
                    },
                  },
                ]}
                getValueFromEvent={(e) =>
                  e.target.value === '' ? null : Number(e.target.value)
                }
              >
                <Input
                  className="w-full"
                  placeholder="输入本年计划投资"
                  suffix="万元"
                  type="number"
                />
              </Form.Item>
              <Form.Item
                label="项目起始时间"
                name="start_time"
                rules={[{ required: true, message: '请选择项目起始时间' }]}
              >
                <DatePicker
                  className="w-full"
                  placeholder="请选择项目起始时间"
                  format="YYYY-MM-DD"
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="项目预计完成时间"
                name="complete_time_expect"
                rules={[{ required: true, message: '请选择项目预计完成时间' }]}
              >
                <DatePicker
                  className="w-full"
                  placeholder="请选择项目预计完成时间"
                  format="YYYY-MM-DD"
                />
              </Form.Item>
              <Form.Item
                label="预计投资收益率"
                name="investment_return_rate_expect"
                rules={[{ required: true, message: '请输入预计投资收益率' }]}
                getValueFromEvent={(e) =>
                  e.target.value === '' ? null : Number(e.target.value)
                }
              >
                <Input
                  className="w-full"
                  placeholder="输入预计投资收益率"
                  suffix="%"
                  type="number"
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="特殊事项考虑类型"
                name="special_consideration_type"
                rules={[
                  {
                    required:
                      watchInvestorType === 2 || watchInvestorType === 4,
                    message: '请选择特殊事项考虑类型',
                  },
                ]}
              >
                <Select
                  className="w-full"
                  placeholder="请选择特殊事项考虑类型"
                  options={SPECIAL_CONSIDERATION_TYPE}
                  allowClear
                />
              </Form.Item>
              <Form.Item
                label="特殊事项对应投资额"
                name="special_total_investment"
                rules={[
                  {
                    required:
                      watchInvestorType === 2 || watchInvestorType === 4,
                    message: '请输入特殊事项对应投资额',
                  },
                ]}
                getValueFromEvent={(e) =>
                  e.target.value === '' ? null : Number(e.target.value)
                }
              >
                <Input
                  className="w-full"
                  placeholder="输入特殊事项对应投资额"
                  suffix="万元"
                  type="number"
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1">
              <Form.Item
                label="投资目的及必要性"
                name="investment_necessity"
                rules={[
                  {
                    required:
                      watchInvestorType === 2 || watchInvestorType === 4,
                    message: '请输入投资目的及必要性',
                  },
                ]}
              >
                <Input.TextArea
                  className="w-full"
                  placeholder="输入投资目的及必要性"
                  autoSize={{ minRows: 2, maxRows: 4 }}
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="资金来源-自有资金"
                name="fund_source_self"
                rules={[{ required: true, message: '请输入资金来源-自有资金' }]}
                getValueFromEvent={(e) =>
                  e.target.value === '' ? null : Number(e.target.value)
                }
              >
                <Input
                  className="w-full"
                  placeholder="请输入资金来源-自有资金"
                  type="number"
                  suffix="万元"
                />
              </Form.Item>
              <Form.Item
                label="资金来源-贷款"
                name="fund_source_loan"
                rules={[{ required: true, message: '请输入资金来源-贷款' }]}
                getValueFromEvent={(e) =>
                  e.target.value === '' ? null : Number(e.target.value)
                }
              >
                <Input
                  className="w-full"
                  placeholder="输入资金来源-贷款"
                  suffix="万元"
                  type="number"
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="资金来源-其他"
                name="fund_source_other"
                rules={[{ required: true, message: '请输入资金来源-其他' }]}
                getValueFromEvent={(e) =>
                  e.target.value === '' ? null : Number(e.target.value)
                }
              >
                <Input
                  className="w-full"
                  placeholder="请输入资金来源-其他"
                  type="number"
                  suffix="万元"
                />
              </Form.Item>
              <Form.Item
                label="其他股东及股比情况"
                name="shareholders_info"
                rules={[
                  { required: true, message: '请输入其他股东及股比情况' },
                ]}
              >
                <Input
                  className="w-full"
                  placeholder="输入其他股东及股比情况"
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1">
              <Form.Item label="备注" name="remarks">
                <Input.TextArea
                  className="w-full"
                  placeholder="输入备注"
                  autoSize={{ minRows: 2, maxRows: 4 }}
                />
              </Form.Item>
            </div>
          </div>
          <h4 className="text-sm font-semibold text-[#266EFF]">项目完成情况</h4>
          <div>
            <div className="grid grid-cols-1">
              <Form.Item
                label="项目进度描述"
                name="project_progress_desc"
                rules={[
                  { required: !!isUpdate, message: '请输入项目进度描述' },
                ]}
              >
                <Input.TextArea
                  className="w-full"
                  placeholder="输入项目进度描述"
                  autoSize={{ minRows: 2, maxRows: 4 }}
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="本年度完成投资额"
                name="investment_completed_cur_year"
                dependencies={[
                  'completed_fund_source_self',
                  'completed_fund_source_loan',
                  'completed_fund_source_other',
                ]}
                rules={[
                  { required: !!isUpdate, message: '请输入本年度完成投资额' },
                  {
                    validator: (_, value) => {
                      const current = Number(value ?? 0)
                      const prev = Number(
                        getFormData.data?.investment_completed_cur_year ?? 0,
                      )
                      if (current < prev) {
                        return Promise.reject(
                          new Error(
                            `完成投资额不得小于上一次上报的值: ${prev}`,
                          ),
                        )
                      }

                      const s1 = Number(
                        form.getFieldValue('completed_fund_source_self') ?? 0,
                      )
                      const s2 = Number(
                        form.getFieldValue('completed_fund_source_loan') ?? 0,
                      )
                      const s3 = Number(
                        form.getFieldValue('completed_fund_source_other') ?? 0,
                      )

                      if (current !== s1 + s2 + s3) {
                        return Promise.reject(
                          new Error(
                            '三项完成投资额之和不等于本年实际投资额，请确认！',
                          ),
                        )
                      }
                      return Promise.resolve()
                    },
                  },
                ]}
                getValueFromEvent={(e) =>
                  e.target.value === '' ? null : Number(e.target.value)
                }
              >
                <Input
                  className="w-full"
                  placeholder="输入本年度完成投资额"
                  suffix="万元"
                  type="number"
                />
              </Form.Item>
              <Form.Item
                label="完成投资额-自有资金"
                name="completed_fund_source_self"
                rules={[
                  { required: isUpdate, message: '请输入完成投资额-自有资金' },
                ]}
                getValueFromEvent={(e) =>
                  e.target.value === '' ? null : Number(e.target.value)
                }
              >
                <Input
                  className="w-full"
                  placeholder="输入完成投资额-自有资金"
                  suffix="万元"
                  type="number"
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="完成投资额-贷款"
                name="completed_fund_source_loan"
                rules={[
                  { required: isUpdate, message: '请输入完成投资额-贷款' },
                ]}
                getValueFromEvent={(e) =>
                  e.target.value === '' ? null : Number(e.target.value)
                }
              >
                <Input
                  className="w-full"
                  placeholder="输入完成投资额-贷款"
                  suffix="万元"
                  type="number"
                />
              </Form.Item>
              <Form.Item
                label="完成投资额-其他"
                name="completed_fund_source_other"
                rules={[
                  { required: isUpdate, message: '请输入完成投资额-其他' },
                ]}
                getValueFromEvent={(e) =>
                  e.target.value === '' ? null : Number(e.target.value)
                }
              >
                <Input
                  className="w-full"
                  placeholder="输入完成投资额-其他"
                  suffix="万元"
                  type="number"
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="投资完成后所占股比"
                name="equity_ratio_after_investmen"
                rules={[
                  { required: !!isUpdate, message: '请输入投资完成后所占股比' },
                ]}
                getValueFromEvent={(e) =>
                  e.target.value === '' ? null : Number(e.target.value)
                }
              >
                <Input
                  className="w-full"
                  placeholder="输入投资完成后所占股比"
                  suffix="%"
                  type="number"
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1">
              <Form.Item label="备注" name="completed_remarks">
                <Input.TextArea
                  className="w-full"
                  placeholder="输入备注"
                  autoSize={{ minRows: 2, maxRows: 4 }}
                />
              </Form.Item>
            </div>
          </div>
        </div>
        <div className="sticky bottom-0 flex justify-end gap-2 bg-white py-2">
          <Button type="primary" htmlType="submit">
            {isUpdate ? '更新数据' : '保存数据'}
          </Button>
          <Button
            onClick={() => navigate({ to: '/basic-report/equity-projects' })}
          >
            取消
          </Button>
        </div>
      </Form>
    </Spin>
  )
}
