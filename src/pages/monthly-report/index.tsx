import { useAuth } from '@/contexts/auth'

import { MonthlyReportParentIndexPage } from './components/ParentCompanyIndex'
import { MonthlyReportSubIndexPage } from './components/SubCompanyIndex'

export const MonthlyReportIndexPage = () => {
  const { company } = useAuth()

  const COMPANY_LEVEL = company?.level

  return COMPANY_LEVEL === 3 ? (
    <MonthlyReportSubIndexPage />
  ) : (
    <MonthlyReportParentIndexPage />
  )
}
