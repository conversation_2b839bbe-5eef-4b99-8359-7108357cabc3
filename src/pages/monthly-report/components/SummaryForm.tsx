import { useMutation, useQuery } from '@tanstack/react-query'
import { useNavigate, useParams, useSearch } from '@tanstack/react-router'
import {
  Button,
  Col,
  DatePicker,
  Form,
  Input,
  message,
  Row,
  Select,
  Spin,
} from 'antd'
import dayjs from 'dayjs'
import { useEffect, useState } from 'react'

import { useAuth } from '@/contexts/auth.tsx'
import { type APIResponse, request } from '@/lib/request.ts'
import type {
  MonthlyReportDTO,
  MonthlyReportFormItem,
} from '@/universal/data-summary/types.ts'

const SubCompanyTableForm = ({ data }: { data: MonthlyReportDTO }) => {
  return (
    <table className="flex-shrink-0 border-collapse border border-r-0 border-[#EAEAEA] [&_td]:!h-12 [&_td]:border [&_td]:border-[#EAEAEA] [&_td]:text-center [&_td]:text-nowrap">
      <thead>
        <tr className="bg-[#E5EBFE] text-nowrap [&>th]:p-2 [&>th]:font-normal">
          <th>{data.company_name}</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>{data.total_amount}</td>
        </tr>
        <tr>
          <td>{data.fixed_assets_amount}</td>
        </tr>
        <tr>
          <td>{data.real_estate_amount}</td>
        </tr>
        <tr>
          <td>{data.equity_investment_amount}</td>
        </tr>
        <tr>
          <td>{data.foreign_equity_amount}</td>
        </tr>
        <tr>
          <td>{data.main_business_amount}</td>
        </tr>
        <tr>
          <td>{data.non_main_business_amount}</td>
        </tr>
        <tr>
          <td>{data.domestic_amount}</td>
        </tr>
        <tr>
          <td>{data.overseas_amount}</td>
        </tr>
        <tr>
          <td>{data.key_strategic_industry_amount}</td>
        </tr>
        <tr>
          <td>{data.fixed_assets_amount}</td>
        </tr>
        <tr>
          <td>{data.key_equity_amount}</td>
        </tr>
        <tr>
          <td>{data.key_foreign_amount}</td>
        </tr>
        <tr>
          <td>{data.key_manufacture_amount}</td>
        </tr>
        <tr>
          <td>{data.key_mining_amount}</td>
        </tr>
        <tr>
          <td style={{ width: '240px' }}>
            <Input.TextArea
              autoSize={{ minRows: 3, maxRows: 5 }}
              value={data.monthly_analysis}
              disabled
            />
          </td>
        </tr>
      </tbody>
    </table>
  )
}

export const SummaryForm = ({ isView }: { isView?: boolean }) => {
  const { user } = useAuth()
  // const { company } = useAuth()

  const { id: approvalNodeId } = useParams({ strict: false })
  const { reportPeriod } = useSearch({ strict: false })
  const { itemId } = useSearch({ strict: false })

  const navigate = useNavigate()

  const [currentReportPeriod, setCurrentReportPeriod] = useState(
    reportPeriod || dayjs().startOf('month').format('YYYY-MM-DD HH:mm:ss'),
  )

  const [form] = Form.useForm()

  const [lastMonthData, setLastMonthData] = useState<MonthlyReportDTO>()
  const [lastYearData, setLastYearData] = useState<MonthlyReportDTO>()

  const getVerificationResult = (current: number, last: number) => {
    if (current === last) return null
    return current > last ? (
      ''
    ) : (
      <span className="text-[#FF4C4C]">较上月减少</span>
    )
  }

  const getInitializationData = useQuery({
    queryKey: [currentReportPeriod, approvalNodeId],
    queryFn: async ({ queryKey: [period, approval_node_id] }) => {
      form.resetFields()
      const res = await request<
        APIResponse<{
          cur_mon: MonthlyReportDTO
          pre_mon: MonthlyReportDTO
          pre_year_mon: MonthlyReportDTO
          list: MonthlyReportDTO[]
        }>
      >('/aggregate/monthly-report/aggregate-detail', {
        query: { period, approval_node_id },
      })

      if (res.code !== 200001) {
        message.error(res.message)
        return null
      }

      form.setFieldsValue({
        ...res.data.cur_mon,
        period: dayjs(res.data.cur_mon.period).startOf('month'),
      })

      setLastMonthData(res.data.pre_mon)
      setLastYearData(res.data.pre_year_mon)

      return res.data
    },
    refetchOnWindowFocus: false,
  })

  const updateForm = useMutation({
    mutationFn: async (values: MonthlyReportFormItem) => {
      try {
        await form.validateFields()
      } catch {
        return message.error('请填写所有必填字段')
      }

      const { period, ...data } = values
      const bodyData = {
        ...data,
        period: dayjs(period).startOf('month').format('YYYY-MM-DD HH:mm:ss'),
      }

      const res = await request<APIResponse<null>>(
        '/aggregate/monthly-report/modify',
        {
          method: 'PUT',
          body: {
            ...getInitializationData.data?.cur_mon,
            ...bodyData,
            id: itemId,
          },
        },
      )

      if (res.code === 200001) {
        getInitializationData.refetch()
        message.success('保存成功')
        return
      }
      message.error(res.message)
    },
    onError: (err) => message.error(JSON.stringify(err)),
  })

  const reportForm = useMutation({
    mutationKey: ['report'],
    mutationFn: async () => {
      const ids = [
        approvalNodeId,
        ...(getInitializationData.data?.list?.map(
          (item) => item.approval_node_id,
        ) ?? []),
      ]

      const res = await request<APIResponse<null>>(
        '/aggregate/monthly-report/pending-mul',
        {
          method: 'POST',
          body: { node_ids: ids },
        },
      )

      if (res.code === 200001) {
        message.success('上报成功')
        await navigate({ to: '/data-summary/monthly-report' })
        return
      }
      message.error(res.message)
    },

    onError: (err) => message.error(JSON.stringify(err)),
  })

  const {
    total_amount: thisMonthTotalAmount,
    fixed_assets_amount: thisMonthFixedAssetsAmount,
    real_estate_amount: thisMonthRealEstateAmount,
    equity_investment_amount: thisMonthEquityInvestmentAmount,
    foreign_equity_amount: thisMonthForeignEquityAmount,
    main_business_amount: thisMonthMainBusinessAmount,
    non_main_business_amount: thisMonthNonMainBusinessAmount,
    domestic_amount: thisMonthDomesticAmount,
    overseas_amount: thisMonthOverseasAmount,
    key_strategic_industry_amount: thisMonthKeyStrategicIndustryAmount,
    key_fixed_assets_amount: thisMonthKeyFixedAssetsAmount,
    key_equity_amount: thisMonthKeyEquityAmount,
    key_foreign_amount: thisMonthKeyForeignAmount,
  } = form.getFieldsValue([
    'total_amount',
    'fixed_assets_amount',
    'real_estate_amount',
    'equity_investment_amount',
    'foreign_equity_amount',
    'main_business_amount',
    'non_main_business_amount',
    'domestic_amount',
    'overseas_amount',
    'key_strategic_industry_amount',
    'key_fixed_assets_amount',
    'key_equity_amount',
    'key_foreign_amount',
  ])

  const watchKeyManufactureAmount = Form.useWatch(
    'key_manufacture_amount',
    form,
  )
  const watchKeyMiningAmount = Form.useWatch('key_mining_amount', form)

  useEffect(() => {
    const fixedAssetsAmount = Number(thisMonthFixedAssetsAmount ?? 0)
    const equityInvestmentAmount = Number(thisMonthEquityInvestmentAmount ?? 0)

    form.setFieldValue(
      'total_amount',
      fixedAssetsAmount + equityInvestmentAmount,
    )
  }, [thisMonthFixedAssetsAmount, thisMonthEquityInvestmentAmount, form])

  return (
    <Spin
      spinning={
        getInitializationData.isFetching ||
        updateForm.isPending ||
        reportForm.isPending
      }
    >
      <Form
        form={form}
        onFinish={async (values) => {
          await updateForm.mutateAsync(values)
          await reportForm.mutateAsync()
        }}
        disabled={isView}
      >
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              label="填报周期"
              name="period"
              rules={[{ required: true, message: '请选择填报周期' }]}
            >
              <DatePicker
                picker="month"
                className="w-full"
                placeholder="请选择填报周期"
                onChange={(value) =>
                  setCurrentReportPeriod(
                    dayjs(value).startOf('month').format('YYYY-MM-DD HH:mm:ss'),
                  )
                }
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="编制单位"
              name="company_id"
              rules={[{ required: true, message: '请选择编制单位' }]}
            >
              <Select
                placeholder="请选择编制单位"
                options={[{ label: user?.company, value: user?.company_id }]}
                onChange={(_, option) => {
                  const selectedOption = Array.isArray(option)
                    ? option[0]
                    : option
                  form.setFieldValue('company_name', selectedOption?.label)
                }}
              />
            </Form.Item>
            <Form.Item name="company_name" noStyle />
          </Col>
        </Row>

        <Row>
          <Col span={24} className="border border-[#EAEAEA]">
            <div className="flex flex-nowrap overflow-x-auto">
              <table className="flex-shrink-0 border-collapse border border-r-0 border-[#EAEAEA] [&_.ant-form-item]:!mb-0 [&_td]:!h-12 [&_td]:border [&_td]:border-[#EAEAEA] [&_td]:text-center [&_td]:text-nowrap">
                <thead>
                  <tr className="bg-[#E5EBFE] text-nowrap [&>th]:p-2 [&>th]:font-normal">
                    <th colSpan={2}>类型</th>
                    <th>截止本月数据(万元)</th>
                    <th>截止上月数据(万元)</th>
                    <th>去年同期(万元)</th>
                    <th>校验结果</th>
                    <th>备注说明</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td colSpan={2}>合计</td>
                    <td>
                      <Form.Item
                        name="total_amount"
                        getValueFromEvent={(e) =>
                          e.target.value === '' ? null : Number(e.target.value)
                        }
                        rules={[{ required: true, message: '请输入合计' }]}
                      >
                        <Input type="number" disabled />
                      </Form.Item>
                    </td>
                    <td>{lastMonthData?.total_amount}</td>
                    <td>{lastYearData?.total_amount}</td>
                    <td>
                      {getVerificationResult(
                        thisMonthTotalAmount,
                        lastMonthData?.total_amount ?? 0,
                      )}
                    </td>
                    <td>
                      <Form.Item
                        name="total_amount_remarks"
                        rules={[
                          {
                            required:
                              thisMonthTotalAmount <
                              (lastMonthData?.total_amount ?? 0),
                            message: '请输入备注说明',
                          },
                        ]}
                      >
                        <Input placeholder="请输入" />
                      </Form.Item>
                    </td>
                  </tr>

                  <tr>
                    <td rowSpan={4}>投资方式</td>
                    <td>固定资产</td>
                    <td>
                      <Form.Item
                        name="fixed_assets_amount"
                        rules={[{ required: true, message: '请输入固定资产' }]}
                        getValueFromEvent={(e) =>
                          e.target.value === '' ? null : Number(e.target.value)
                        }
                      >
                        <Input type="number" disabled />
                      </Form.Item>
                    </td>
                    <td>{lastMonthData?.fixed_assets_amount}</td>
                    <td>{lastYearData?.fixed_assets_amount}</td>
                    <td>
                      {getVerificationResult(
                        thisMonthFixedAssetsAmount,
                        lastMonthData?.fixed_assets_amount ?? 0,
                      )}
                    </td>
                    <td>
                      <Form.Item
                        name="fixed_assets_amount_remark"
                        rules={[
                          {
                            required:
                              thisMonthFixedAssetsAmount <
                              (lastMonthData?.fixed_assets_amount ?? 0),
                            message: '请输入备注说明',
                          },
                        ]}
                      >
                        <Input placeholder="请输入" />
                      </Form.Item>
                    </td>
                  </tr>
                  <tr>
                    <td>其中:房地产</td>
                    <td>
                      <Form.Item
                        name="real_estate_amount"
                        rules={[{ required: true, message: '请输入房地产' }]}
                        getValueFromEvent={(e) =>
                          e.target.value === '' ? null : Number(e.target.value)
                        }
                      >
                        <Input type="number" disabled />
                      </Form.Item>
                    </td>
                    <td>{lastMonthData?.real_estate_amount}</td>
                    <td>{lastYearData?.real_estate_amount}</td>
                    <td>
                      {getVerificationResult(
                        thisMonthRealEstateAmount,
                        lastMonthData?.real_estate_amount ?? 0,
                      )}
                    </td>
                    <td>
                      <Form.Item
                        name="real_estate_amount_remark"
                        rules={[
                          {
                            required:
                              thisMonthRealEstateAmount <
                              (lastMonthData?.real_estate_amount ?? 0),
                            message: '请输入备注说明',
                          },
                        ]}
                      >
                        <Input placeholder="请输入" />
                      </Form.Item>
                    </td>
                  </tr>
                  <tr>
                    <td>股权投资</td>
                    <td>
                      <Form.Item
                        name="equity_investment_amount"
                        rules={[{ required: true, message: '请输入股权投资' }]}
                        getValueFromEvent={(e) =>
                          e.target.value === '' ? null : Number(e.target.value)
                        }
                      >
                        <Input type="number" disabled />
                      </Form.Item>
                    </td>
                    <td>{lastMonthData?.equity_investment_amount}</td>
                    <td>{lastYearData?.equity_investment_amount}</td>
                    <td>
                      {getVerificationResult(
                        thisMonthEquityInvestmentAmount,
                        lastMonthData?.equity_investment_amount ?? 0,
                      )}
                    </td>
                    <td>
                      <Form.Item
                        name="equity_investment_amount_remark"
                        rules={[
                          {
                            required:
                              thisMonthEquityInvestmentAmount <
                              (lastMonthData?.equity_investment_amount ?? 0),
                            message: '请输入备注说明',
                          },
                        ]}
                      >
                        <Input placeholder="请输入" />
                      </Form.Item>
                    </td>
                  </tr>
                  <tr>
                    <td>其中:对外并购</td>
                    <td>
                      <Form.Item
                        name="foreign_equity_amount"
                        rules={[{ required: true, message: '请输入对外并购' }]}
                        getValueFromEvent={(e) =>
                          e.target.value === '' ? null : Number(e.target.value)
                        }
                      >
                        <Input type="number" disabled />
                      </Form.Item>
                    </td>
                    <td>{lastMonthData?.foreign_equity_amount}</td>
                    <td>{lastYearData?.foreign_equity_amount}</td>
                    <td>
                      {getVerificationResult(
                        thisMonthForeignEquityAmount,
                        lastMonthData?.foreign_equity_amount ?? 0,
                      )}
                    </td>
                    <td>
                      <Form.Item
                        name="foreign_equity_amount_remark"
                        rules={[
                          {
                            required:
                              thisMonthForeignEquityAmount <
                              (lastMonthData?.foreign_equity_amount ?? 0),
                            message: '请输入备注说明',
                          },
                        ]}
                      >
                        <Input placeholder="请输入" />
                      </Form.Item>
                    </td>
                  </tr>
                  <tr>
                    <td rowSpan={2}>投资方向</td>
                    <td>主业</td>
                    <td>
                      <Form.Item
                        name="main_business_amount"
                        rules={[{ required: true, message: '请输入主业' }]}
                        getValueFromEvent={(e) =>
                          e.target.value === '' ? null : Number(e.target.value)
                        }
                      >
                        <Input type="number" disabled />
                      </Form.Item>
                    </td>
                    <td>{lastMonthData?.main_business_amount}</td>
                    <td>{lastYearData?.main_business_amount}</td>
                    <td>
                      {getVerificationResult(
                        thisMonthMainBusinessAmount,
                        lastMonthData?.main_business_amount ?? 0,
                      )}
                    </td>
                    <td>
                      <Form.Item
                        name="main_business_amount_remark"
                        rules={[
                          {
                            required:
                              thisMonthMainBusinessAmount <
                              (lastMonthData?.main_business_amount ?? 0),
                            message: '请输入备注说明',
                          },
                        ]}
                      >
                        <Input placeholder="请输入" />
                      </Form.Item>
                    </td>
                  </tr>
                  <tr>
                    <td>非主业</td>
                    <td>
                      <Form.Item
                        name="non_main_business_amount"
                        rules={[{ required: true, message: '请输入非主业' }]}
                        getValueFromEvent={(e) =>
                          e.target.value === '' ? null : Number(e.target.value)
                        }
                      >
                        <Input type="number" disabled />
                      </Form.Item>
                    </td>
                    <td>{lastMonthData?.non_main_business_amount}</td>
                    <td>{lastYearData?.non_main_business_amount}</td>
                    <td>
                      {getVerificationResult(
                        thisMonthNonMainBusinessAmount,
                        lastMonthData?.non_main_business_amount ?? 0,
                      )}
                    </td>
                    <td>
                      <Form.Item
                        name="non_main_business_amount_remark"
                        rules={[
                          {
                            required:
                              thisMonthNonMainBusinessAmount <
                              (lastMonthData?.non_main_business_amount ?? 0),
                            message: '请输入备注说明',
                          },
                        ]}
                      >
                        <Input placeholder="请输入" />
                      </Form.Item>
                    </td>
                  </tr>
                  <tr>
                    <td rowSpan={2}>投资区域</td>
                    <td>境内</td>
                    <td>
                      <Form.Item
                        name="domestic_amount"
                        rules={[{ required: true, message: '请输入境内' }]}
                        getValueFromEvent={(e) =>
                          e.target.value === '' ? null : Number(e.target.value)
                        }
                      >
                        <Input type="number" disabled />
                      </Form.Item>
                    </td>
                    <td>{lastMonthData?.domestic_amount}</td>
                    <td>{lastYearData?.domestic_amount}</td>
                    <td>
                      {getVerificationResult(
                        thisMonthDomesticAmount,
                        lastMonthData?.domestic_amount ?? 0,
                      )}
                    </td>
                    <td>
                      <Form.Item
                        name="domestic_amount_remark"
                        rules={[
                          {
                            required:
                              thisMonthDomesticAmount <
                              (lastMonthData?.domestic_amount ?? 0),
                            message: '请输入备注说明',
                          },
                        ]}
                      >
                        <Input placeholder="请输入" />
                      </Form.Item>
                    </td>
                  </tr>
                  <tr>
                    <td>境外</td>
                    <td>
                      <Form.Item
                        name="overseas_amount"
                        rules={[{ required: true, message: '请输入境外' }]}
                        getValueFromEvent={(e) =>
                          e.target.value === '' ? null : Number(e.target.value)
                        }
                      >
                        <Input type="number" disabled />
                      </Form.Item>
                    </td>
                    <td>{lastMonthData?.overseas_amount}</td>
                    <td>{lastYearData?.overseas_amount}</td>
                    <td>
                      {getVerificationResult(
                        thisMonthOverseasAmount,
                        lastMonthData?.overseas_amount ?? 0,
                      )}
                    </td>
                    <td>
                      <Form.Item
                        name="overseas_amount_remark"
                        rules={[
                          {
                            required:
                              thisMonthOverseasAmount <
                              (lastMonthData?.overseas_amount ?? 0),
                            message: '请输入备注说明',
                          },
                        ]}
                      >
                        <Input placeholder="请输入" />
                      </Form.Item>
                    </td>
                  </tr>
                  <tr>
                    <td rowSpan={6}>重点领域</td>
                    <td>战略性新兴产业</td>
                    <td>
                      <Form.Item
                        name="key_strategic_industry_amount"
                        rules={[
                          { required: true, message: '请输入战略性新兴产业' },
                        ]}
                        getValueFromEvent={(e) =>
                          e.target.value === '' ? null : Number(e.target.value)
                        }
                      >
                        <Input type="number" disabled />
                      </Form.Item>
                    </td>
                    <td>{lastMonthData?.key_strategic_industry_amount}</td>
                    <td>{lastYearData?.key_strategic_industry_amount}</td>
                    <td>
                      {getVerificationResult(
                        thisMonthKeyStrategicIndustryAmount,
                        lastMonthData?.key_strategic_industry_amount ?? 0,
                      )}
                    </td>
                    <td>
                      <Form.Item
                        name="key_strategic_industry_amount_remark"
                        rules={[
                          {
                            required:
                              thisMonthKeyStrategicIndustryAmount <
                              (lastMonthData?.key_strategic_industry_amount ??
                                0),
                            message: '请输入备注说明',
                          },
                        ]}
                      >
                        <Input placeholder="请输入" />
                      </Form.Item>
                    </td>
                  </tr>
                  <tr>
                    <td>其中:固定资产投资</td>
                    <td>
                      <Form.Item
                        name="key_fixed_assets_amount"
                        rules={[
                          { required: true, message: '请输入固定资产投资' },
                        ]}
                        getValueFromEvent={(e) =>
                          e.target.value === '' ? null : Number(e.target.value)
                        }
                      >
                        <Input type="number" disabled />
                      </Form.Item>
                    </td>
                    <td>{lastMonthData?.key_fixed_assets_amount}</td>
                    <td>{lastYearData?.key_fixed_assets_amount}</td>
                    <td>
                      {getVerificationResult(
                        thisMonthKeyFixedAssetsAmount,
                        lastMonthData?.key_fixed_assets_amount ?? 0,
                      )}
                    </td>
                    <td>
                      <Form.Item
                        name="key_fixed_assets_amount_remark"
                        rules={[
                          {
                            required:
                              thisMonthKeyFixedAssetsAmount <
                              (lastMonthData?.key_fixed_assets_amount ?? 0),
                            message: '请输入备注说明',
                          },
                        ]}
                      >
                        <Input placeholder="请输入" />
                      </Form.Item>
                    </td>
                  </tr>
                  <tr>
                    <td>股权投资</td>
                    <td>
                      <Form.Item
                        name="key_equity_amount"
                        rules={[{ required: true, message: '请输入股权投资' }]}
                        getValueFromEvent={(e) =>
                          e.target.value === '' ? null : Number(e.target.value)
                        }
                      >
                        <Input type="number" disabled />
                      </Form.Item>
                    </td>
                    <td>{lastMonthData?.key_equity_amount}</td>
                    <td>{lastYearData?.key_equity_amount}</td>
                    <td>
                      {getVerificationResult(
                        thisMonthKeyEquityAmount,
                        lastMonthData?.key_equity_amount ?? 0,
                      )}
                    </td>
                    <td>
                      <Form.Item
                        name="key_equity_amount_remark"
                        rules={[
                          {
                            required:
                              thisMonthKeyEquityAmount <
                              (lastMonthData?.key_equity_amount ?? 0),
                            message: '请输入备注说明',
                          },
                        ]}
                      >
                        <Input placeholder="请输入" />
                      </Form.Item>
                    </td>
                  </tr>
                  <tr>
                    <td>对外并购</td>
                    <td>
                      <Form.Item
                        name="key_foreign_amount"
                        rules={[{ required: true, message: '请输入对外并购' }]}
                        getValueFromEvent={(e) =>
                          e.target.value === '' ? null : Number(e.target.value)
                        }
                      >
                        <Input type="number" disabled />
                      </Form.Item>
                    </td>
                    <td>{lastMonthData?.key_foreign_amount}</td>
                    <td>{lastYearData?.key_foreign_amount}</td>
                    <td>
                      {getVerificationResult(
                        thisMonthKeyForeignAmount,
                        lastMonthData?.key_foreign_amount ?? 0,
                      )}
                    </td>
                    <td>
                      <Form.Item
                        name="key_foreign_amount_remark"
                        rules={[
                          {
                            required:
                              thisMonthKeyForeignAmount <
                              (lastMonthData?.key_foreign_amount ?? 0),
                            message: '请输入备注说明',
                          },
                        ]}
                      >
                        <Input placeholder="请输入" />
                      </Form.Item>
                    </td>
                  </tr>
                  <tr>
                    <td>制造业</td>
                    <td>
                      <Form.Item
                        name="key_manufacture_amount"
                        rules={[{ required: true, message: '请输入制造业' }]}
                        getValueFromEvent={(e) =>
                          e.target.value === '' ? null : Number(e.target.value)
                        }
                      >
                        <Input type="number" />
                      </Form.Item>
                    </td>
                    <td>{lastMonthData?.key_manufacture_amount}</td>
                    <td>{lastYearData?.key_manufacture_amount}</td>
                    <td>
                      {getVerificationResult(
                        watchKeyManufactureAmount,
                        lastMonthData?.key_manufacture_amount ?? 0,
                      )}
                    </td>
                    <td>
                      <Form.Item
                        name="key_manufacture_amount_remark"
                        rules={[
                          {
                            required:
                              watchKeyManufactureAmount <
                              (lastMonthData?.key_manufacture_amount ?? 0),
                            message: '请输入备注说明',
                          },
                        ]}
                      >
                        <Input placeholder="请输入" />
                      </Form.Item>
                    </td>
                  </tr>
                  <tr>
                    <td>采矿业</td>
                    <td>
                      <Form.Item
                        name="key_mining_amount"
                        rules={[{ required: true, message: '请输入采矿业' }]}
                        getValueFromEvent={(e) =>
                          e.target.value === '' ? null : Number(e.target.value)
                        }
                      >
                        <Input type="number" />
                      </Form.Item>
                    </td>
                    <td>{lastMonthData?.key_mining_amount}</td>
                    <td>{lastYearData?.key_mining_amount}</td>
                    <td>
                      {getVerificationResult(
                        watchKeyMiningAmount,
                        lastMonthData?.key_mining_amount ?? 0,
                      )}
                    </td>
                    <td>
                      <Form.Item
                        name="key_mining_amount_remark"
                        rules={[
                          {
                            required:
                              watchKeyMiningAmount <
                              (lastMonthData?.key_mining_amount ?? 0),
                            message: '请输入备注说明',
                          },
                        ]}
                      >
                        <Input placeholder="请输入" />
                      </Form.Item>
                    </td>
                  </tr>
                  <tr>
                    <td
                      colSpan={2}
                      style={{
                        textAlign: 'left',
                        textWrap: 'wrap',
                        width: '240px',
                      }}
                    >
                      月度分析(简述行业趋势分析及其对投资的影响、投资主要方向及变化、重大项目关键节点进展，150字以内)
                    </td>
                    <td colSpan={5}>
                      <Form.Item
                        name="monthly_analysis"
                        rules={[{ required: true, message: '请输入月度分析' }]}
                      >
                        <Input.TextArea
                          autoSize={{ minRows: 3, maxRows: 5 }}
                          maxLength={150}
                        />
                      </Form.Item>
                    </td>
                  </tr>
                </tbody>
              </table>
              {getInitializationData?.data?.list?.map((item) => (
                <SubCompanyTableForm key={item.id} data={item} />
              ))}
            </div>
          </Col>
        </Row>
        <Row className="flex justify-end gap-2 py-2">
          {isView ? (
            <Button
              onClick={() => navigate({ to: '/data-summary/monthly-report' })}
              disabled={false}
            >
              返回
            </Button>
          ) : (
            <>
              {/* <Button type="primary" htmlType="submit">
                {company?.level === 2 ? '汇总并上报集团' : '汇总并上报国资委'}
              </Button> */}

              <Button
                type="primary"
                onClick={async () => {
                  await updateForm.mutateAsync(form.getFieldsValue())
                  getInitializationData.refetch()
                }}
              >
                保存数据
              </Button>

              <Button
                onClick={() => navigate({ to: '/data-summary/monthly-report' })}
              >
                取消
              </Button>
            </>
          )}
        </Row>
      </Form>
    </Spin>
  )
}
