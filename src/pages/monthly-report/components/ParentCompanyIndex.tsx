import { Tabs } from 'antd'

import { ParentCompanyAllDataList } from './ParentCompanyAllDataList'
import { ParentCompanySummaryList } from './ParentCompanySummaryList'
import { SummaryCard } from './SummaryCard'
export const MonthlyReportParentIndexPage = () => {
  return (
    <div className="flex h-full flex-col gap-4">
      <Tabs
        items={[
          {
            key: '1',
            label: '汇总数据',
            children: (
              <>
                <SummaryCard consolidation={1} />
                <ParentCompanySummaryList />
              </>
            ),
          },
          {
            key: '2',
            label: '所有数据',
            children: (
              <>
                <SummaryCard consolidation={2} />
                <ParentCompanyAllDataList />
              </>
            ),
          },
        ]}
      ></Tabs>
    </div>
  )
}
