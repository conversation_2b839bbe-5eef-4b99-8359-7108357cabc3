import { useMutation, useQuery } from '@tanstack/react-query'
import { Link } from '@tanstack/react-router'
import {
  <PERSON><PERSON>,
  Card,
  DatePicker,
  Divider,
  Form,
  message,
  Popconfirm,
  Select,
  Table,
  type TableColumnsType,
  Tag,
  Tooltip,
} from 'antd'
import dayjs from 'dayjs'
import { FilterIcon } from 'lucide-react'
import { useState } from 'react'

import { FormItemPrefix } from '@/components/FormItemPrefix.tsx'
import { useAuth } from '@/contexts/auth'
import { useCompany } from '@/contexts/company'
import { type APIResponse, request } from '@/lib/request.ts'
import { checkUndefinedProperties, flattenTreeToArray } from '@/universal'
import { getApprovalStatusColor } from '@/universal/data-summary'
import { APPROVAL_STATUS } from '@/universal/data-summary/constants.ts'
import { ReportModal } from '@/universal/data-summary/Report'
import type { MonthlyReportDTO } from '@/universal/data-summary/types.ts'
import { RejectModal } from '@/universal/Reject'

import { SummaryCard } from './SummaryCard'

export const MonthlyReportSubIndexPage = () => {
  const { company } = useAuth()
  const { companyTree } = useCompany()

  const [form] = Form.useForm()

  const [filters, setFilters] = useState({})
  const [pagination, setPagination] = useState({ page_num: 1, page_size: 10 })

  const [selectedRows, setSelectedRows] = useState<MonthlyReportDTO[]>([])

  const [rejectOpen, setRejectOpen] = useState(false)
  const [rejectKey, setRejectKey] = useState('')

  const getTableData = useQuery({
    queryKey: [pagination, filters],
    queryFn: async ({ queryKey: [pagination, filters] }) => {
      const response = await request<
        APIResponse<{ Data: MonthlyReportDTO[]; Total: number }>
      >('/aggregate/monthly-report/list', {
        query: { use_total: true, ...pagination, ...filters },
      })
      if (response.code !== 200001) {
        message.error(response.message)
        return null
      }
      return response.data
    },
    staleTime: 0,
    retry: false,
  })

  const handleDelete = useMutation({
    mutationFn: async (ids: string[] | undefined) => {
      const res = await request<APIResponse<null>>(
        '/aggregate/monthly-report',
        {
          method: 'DELETE',
          body: {
            monthly_ids: ids,
          },
        },
      )
      if (res.code === 200001) {
        message.success('操作成功')
        getTableData.refetch()
        return
      }
      message.error(res?.message)
    },
    onError: (err) => message.error(JSON.stringify(err)),
  })

  const columns: TableColumnsType<MonthlyReportDTO> = [
    {
      title: '序号',
      align: 'center',
      render: (_, __, index) => index + 1,
      minWidth: 50,
    },
    {
      title: '填报周期',
      dataIndex: 'period',
      render: (value) => dayjs(value).format('YYYY-MM'),
      minWidth: 200,
    },
    {
      title: '编制单位',
      dataIndex: 'company_name',
      render: (value, record) => {
        const companyLevel = flattenTreeToArray(companyTree).filter(
          (item) => item.id === record.company_id,
        )[0]?.level

        return companyLevel === company?.level ? (
          <span>{value + '（本级）'}</span>
        ) : (
          <span>{value}</span>
        )
      },

      minWidth: 100,
    },
    {
      title: '截止本月投资(万元)',
      dataIndex: 'total_amount',
      minWidth: 120,
    },
    {
      title: '状态',
      dataIndex: 'approval_node_status',
      width: 120,
      render: (value, record) => {
        return (
          <Tooltip title={record.reject_reason}>
            <Tag color={getApprovalStatusColor(value)}>
              {APPROVAL_STATUS.find((item) => item.value === value)?.label}
            </Tag>
          </Tooltip>
        )
      },
    },
    // {
    //   title: '上报截止时间',
    //   dataIndex: '',
    //   minWidth: 250,
    // },
    {
      title: '创建人',
      dataIndex: 'creator_name',
      minWidth: 100,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
      minWidth: 100,
    },
    {
      title: '操作',
      fixed: 'right',
      render: (_, record) => {
        return (
          <>
            <Link
              to="/data-summary/monthly-report/$id/update"
              params={{ id: record.id }}
              search={{
                reportPeriod: record.period,
                companyId: record.company_id,
              }}
            >
              <Button type="link" size="small">
                编辑
              </Button>
            </Link>
            <Divider type="vertical" />
            {/* <Button type="link" size="small">
              修改记录
            </Button>
            <Divider type="vertical" /> */}

            {record.reject_view === 1 && (
              <>
                <Button
                  type="link"
                  size="small"
                  onClick={() => {
                    setRejectKey(record.approval_node_id)
                    setRejectOpen(true)
                  }}
                >
                  <span className="text-[#CC8B07]">驳回</span>
                </Button>
                <Divider type="vertical" />
              </>
            )}

            {record.delete_view === 1 && (
              <Popconfirm
                title="确认删除？"
                okText="确认"
                cancelText="取消"
                onConfirm={() => handleDelete.mutate([record.id])}
              >
                <Button type="link" danger size="small">
                  删除
                </Button>
              </Popconfirm>
            )}
          </>
        )
      },
    },
  ]

  return (
    <div className="flex h-full flex-col gap-4">
      <RejectModal
        open={rejectOpen}
        setOpen={setRejectOpen}
        rejectKey={rejectKey}
        rejectUrl="/aggregate/monthly-report/reject-mul"
      />

      <SummaryCard consolidation={2} />

      <Card>
        <div className="flex flex-col gap-4">
          <Form
            form={form}
            onFinish={(values) => {
              if (checkUndefinedProperties(values)) getTableData.refetch()
              const { date, period, ...data } = values
              const [start_time, end_time] = date || []

              setFilters({
                ...data,
                period:
                  period &&
                  dayjs(period).startOf('month').format('YYYY-MM-DD HH:mm:ss'),
                start_time:
                  start_time && dayjs(start_time).format('YYYY-MM-DD HH:mm:ss'),
                end_time:
                  end_time && dayjs(end_time).format('YYYY-MM-DD HH:mm:ss'),
              })
            }}
            onReset={() => {
              setFilters({})
              setPagination({ page_num: 1, page_size: 10 })
            }}
          >
            <div className="flex items-end gap-2">
              <div className="grid flex-1 grid-cols-3 gap-4 2xl:grid-cols-6">
                <Form.Item className="!mb-0" name="period">
                  <DatePicker
                    className="w-full"
                    placeholder="请选择填报周期"
                    prefix={<FormItemPrefix title="填报周期" />}
                    picker="month"
                  />
                </Form.Item>
                <Form.Item className="!mb-0" name="company_id">
                  <Select
                    className="w-full"
                    placeholder="请选择填报单位"
                    prefix={<FormItemPrefix title="填报单位" />}
                    showSearch
                    optionFilterProp="label"
                    options={flattenTreeToArray(companyTree).map((item) => ({
                      label: item.name,
                      value: item.id,
                    }))}
                  />
                </Form.Item>
                <Form.Item className="!mb-0" name="date">
                  <DatePicker.RangePicker
                    className="w-full"
                    prefix={<FormItemPrefix title="创建时间" />}
                  />
                </Form.Item>
              </div>
              <div className="flex shrink-0 grow-0 items-center gap-2">
                <Button
                  type="default"
                  icon={<FilterIcon className="size-3.5" />}
                />
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={getTableData.isFetching}
                >
                  搜索
                </Button>
                <Button
                  type="text"
                  htmlType="reset"
                  loading={getTableData.isFetching}
                >
                  清空
                </Button>
              </div>
            </div>
          </Form>
          <div className="flex items-center justify-end gap-2">
            <Link to="/data-summary/monthly-report/create">
              <Button type="primary">新建数据</Button>
            </Link>
            <ReportModal
              selectedKeys={selectedRows.map((row) => row.approval_node_id)}
            />
            {/* <Button>导入数据</Button>
            <Button>导出数据</Button> */}
            <Popconfirm
              title="确认删除所选项？"
              okText="确认"
              cancelText="取消"
              onConfirm={() =>
                handleDelete.mutate(selectedRows.map((row) => row.id))
              }
            >
              <Button danger disabled={selectedRows.length < 1}>
                批量删除
              </Button>
            </Popconfirm>
          </div>
          <Table
            loading={getTableData.isFetching}
            size="small"
            rowSelection={{
              type: 'checkbox',
              columnWidth: 40,
              align: 'center',
              onChange(_, selectedRows) {
                setSelectedRows(selectedRows)
              },
            }}
            dataSource={getTableData.data?.Data}
            columns={columns}
            scroll={{ x: 'max-content' }}
            sticky={{ offsetHeader: 48 }}
            pagination={{
              showQuickJumper: true,
              showSizeChanger: true,
              total: getTableData.data?.Total,
              onChange: (page, pageSize) => {
                setPagination({ page_num: page, page_size: pageSize })
              },
            }}
            rowKey="approval_node_id"
          />
        </div>
      </Card>
    </div>
  )
}
