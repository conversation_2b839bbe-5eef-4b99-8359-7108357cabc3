import { useQuery } from '@tanstack/react-query'
import { Link, useNavigate, useSearch } from '@tanstack/react-router'
import { Button, Card, Table, type TableColumnsType } from 'antd'
import { CalendarClockIcon } from 'lucide-react'
import numeral from 'numeral'
import { useMemo, useState } from 'react'

import SkeletonTable, {
  type SkeletonTableColumnsType,
} from '@/components/SkeletonTable'
import { request } from '@/lib/request'

import { ReportModal } from './components/modal/Report'
import { InvestmentPlanTableNamesMap } from './constants'

export function InvestmentPlanList() {
  const { id, approval_node_id, company_name } = useSearch({
    strict: false,
  }) as {
    id: string
    approval_node_id: string
    company_name: string
  }
  const [isModalOpen, setIsModalOpen] = useState(false)

  const { data: statisticalData } = useQuery({
    queryKey: ['/aggregate/plan/detail-by-id', id] as const,
    queryFn: async ({ queryKey: [url, id] }) => {
      const response = await request(url as string, {
        query: {
          id,
        },
      })
      if (response.code !== 200001) return null
      return response?.data
    },
    staleTime: 0,
    retry: false,
    enabled: !!id,
  })

  const navigate = useNavigate()

  const tableData = useMemo(() => {
    return Object.entries(InvestmentPlanTableNamesMap).map(([key, value]) => ({
      label: value,
      value: key,
    }))
  }, [])

  const columns = useMemo(() => {
    return [
      {
        title: '序号',
        align: 'center',
        width: 60,
        render: (_, __, index) => {
          return index + 1
        },
      },
      {
        title: '表单名称',
        dataIndex: 'label',
        minWidth: 160,
        render: (label, record) => (
          <Link
            to="/data-summary/investment-plan/table/$tableName"
            search={{
              id,
              approval_node_id,
              company_name,
            }}
            params={{ tableName: record.value }}
          >
            {label}
          </Link>
        ),
      },
    ] as TableColumnsType<(typeof tableData)[number]>
  }, [id, approval_node_id, company_name])

  return (
    <div className="flex h-full flex-col gap-4">
      <Button
        className="-my-4 self-start"
        type="link"
        onClick={() =>
          navigate({
            to: '/data-summary/investment-plan',
          })
        }
      >
        {'<< 返回上一页'}
      </Button>
      <Card>
        <div className="space-y-6">
          <h2 className="text-xl font-semibold">
            {statisticalData?.company_name}
          </h2>
          <div className="flex items-center gap-2">
            <CalendarClockIcon className="size-4" />
            <span className="text-sm text-[#666]">本年度计划投资总额：</span>
            <span className="text-xl font-semibold">
              {numeral(statisticalData?.current_year_total).format('0,0.00')}
              万元
            </span>
          </div>
        </div>
      </Card>
      <Card title={`投资计划-${statisticalData?.investment_year}年`}>
        <SkeletonTable columns={columns as SkeletonTableColumnsType[]}>
          <Table
            rowKey="value"
            size="small"
            pagination={false}
            dataSource={tableData}
            columns={columns}
            scroll={{ x: 'max-content' }}
            sticky={{ offsetHeader: 48 }}
          />
        </SkeletonTable>
        {/* 上报国资委 */}
        <ReportModal
          isOpen={isModalOpen}
          selectedKeys={['1']}
          year={statisticalData?.investment_year}
          onClose={() => setIsModalOpen(false)}
        />
      </Card>
    </div>
  )
}
