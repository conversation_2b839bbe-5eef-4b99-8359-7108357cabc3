import { useQuery } from '@tanstack/react-query'
import { useParams, useSearch } from '@tanstack/react-router'

import { request } from '@/lib/request.ts'

import { InvestmentPlanUpdateTableMap } from './constants'

export function TableUpdate() {
  const params = useParams({ strict: false })
  const { approval_node_id } = useSearch({
    strict: false,
  }) as {
    approval_node_id: string
  }

  const { data } = useQuery({
    queryKey: [
      '/aggregate/plan/detail_by_approval_node_id',
      approval_node_id,
    ] as const,
    queryFn: async ({ queryKey: [url, id] }) => {
      const response = await request(url as string, {
        query: {
          approval_node_id: id,
        },
      })
      if (response.code !== 200001) return null
      return response?.data
    },
    staleTime: 0,
    retry: false,
    enabled: !!approval_node_id,
  })

  const tableName =
    params.tableName as keyof typeof InvestmentPlanUpdateTableMap
  const TableComponent =
    tableName && tableName in InvestmentPlanUpdateTableMap
      ? InvestmentPlanUpdateTableMap[tableName]
      : null

  if (!TableComponent) {
    return null
  }

  return <TableComponent data={data} />
}
