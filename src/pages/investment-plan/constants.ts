import { AreaUpdate } from './components/tableUpdate/AreaUpdate'
import { IndustrySetFormulaUpdate } from './components/tableUpdate/IndustrySetFormulaUpdate'
import { InvestmentPlanUpdate } from './components/tableUpdate/InvestmentPlanUpdate'
import { NonMainBusinessInvestmentPlanUpdate } from './components/tableUpdate/NonMainBusinessInvestmentPlanUpdate'
import { StrategicNewUpdate } from './components/tableUpdate/StrategicNewUpdate'
import { EquityProjectView } from './components/tableView/EquityProject'
import { FixedAssetProjectPlanView } from './components/tableView/FixedAssetProject'
import { NonMainBusinessInvestmentProjectPlanView } from './components/tableView/NonMainBusinessInvestmentProjectPlan'
import { PostEvaluationView } from './components/tableView/PostEvaluation'

export const InvestmentPlan = {
  /** 投资计划情况总表 */
  InvestmentPlan: 'investment-plan',
  /** 战略性新兴产业投资计划情况表*/
  StrategicNew: 'strategic-new',
  /** 按国民经济行业分类投资计划情况表 */
  IndustrySetFormula: 'industry-set-formula',
  /** 按区域分布投资计划情况表 */
  Area: 'area',
  /** 固定资产投资项目计划情况表 */
  FixedAssetProject: 'fixed-asset-project',
  /** 股权投资项目计划情况表 */
  EquityProject: 'equity-project',
  /** 投资后评价项目计划情况表 */
  PostEvaluation: 'post-evaluation',
  /** 非主业投资计划情况总表 */
  NonMainBusinessInvestmentPlan: 'non-main-business-investment-plan',
  /** 非主业投资项目计划情况表 */
  NonMainBusinessInvestmentProjectPlan:
    'non-main-business-investment-project-plan',
} as const

/** 投资计划表相关表名 */
export const InvestmentPlanTableNamesMap = {
  [InvestmentPlan.InvestmentPlan]: '投资计划情况总表',
  [InvestmentPlan.StrategicNew]: '战略性新兴产业投资计划情况表',
  [InvestmentPlan.IndustrySetFormula]: '按国民经济行业分类投资计划情况表',
  [InvestmentPlan.Area]: '按区域分布投资计划情况表',
  [InvestmentPlan.FixedAssetProject]: '固定资产投资项目计划情况表',
  [InvestmentPlan.EquityProject]: '股权投资项目计划情况表',
  [InvestmentPlan.PostEvaluation]: '投资后评价项目计划情况表',
  [InvestmentPlan.NonMainBusinessInvestmentPlan]: '非主业投资计划情况总表',
  [InvestmentPlan.NonMainBusinessInvestmentProjectPlan]:
    '非主业投资项目计划情况表',
}

/** 投资计划表编辑对应组件 */
export const InvestmentPlanUpdateTableMap = {
  [InvestmentPlan.InvestmentPlan]: InvestmentPlanUpdate,
  [InvestmentPlan.StrategicNew]: StrategicNewUpdate,
  [InvestmentPlan.IndustrySetFormula]: IndustrySetFormulaUpdate,
  [InvestmentPlan.Area]: AreaUpdate,
  [InvestmentPlan.NonMainBusinessInvestmentPlan]:
    NonMainBusinessInvestmentPlanUpdate,
  [InvestmentPlan.FixedAssetProject]: FixedAssetProjectPlanView,
  [InvestmentPlan.EquityProject]: EquityProjectView,
  [InvestmentPlan.PostEvaluation]: PostEvaluationView,
  [InvestmentPlan.NonMainBusinessInvestmentProjectPlan]:
    NonMainBusinessInvestmentProjectPlanView,
} as const
