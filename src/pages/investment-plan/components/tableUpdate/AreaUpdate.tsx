import { useQuery, useMutation } from '@tanstack/react-query'
import { useParams, useSearch, useRouter } from '@tanstack/react-router'
import {
  Card,
  Input,
  message,
  Button,
  Table,
  type TableColumnsType,
  InputNumber,
} from 'antd'
import { createStyles } from 'antd-style'
import { useMemo, useState, useEffect, useCallback, memo } from 'react'

import { toNumber, addNumbers } from '@/lib/numberUtils.ts'
import { type APIResponse, request } from '@/lib/request.ts'
import { INVESTMENT_STATUS } from '@/universal/data-summary/constants.ts'

import { InvestmentPlanTableNamesMap } from '../../constants.ts'

import {
  DISABLED_CELL_AREA,
  IMPORTANT_AREA,
  INCLUDE_AREA,
  EXCLUDE_AREA,
  AREA_SORT,
} from './constants.ts'
import type { CalculableAreaField, Area } from './types.ts'

// 优化后的输入组件
const NumberInput = memo(
  ({
    value,
    record,
    field,
    disabled = false,
    onChange,
  }: {
    value: string | number
    record: Area
    field: keyof Area
    disabled?: boolean
    onChange: (record: Area, field: string, value: number) => void
  }) => (
    <InputNumber
      value={toNumber(value)}
      onChange={(val) => val !== null && onChange(record, field, val)}
      disabled={DISABLED_CELL_AREA.includes(record.style) || disabled}
      style={{ width: '100%' }}
      min={0}
      precision={2}
      controls={false}
    />
  ),
)

NumberInput.displayName = 'NumberInput'

const RemarkInput = memo(
  ({
    value,
    record,
    onChange,
  }: {
    value: string
    record: Area
    onChange: (record: Area, field: string, value: string) => void
  }) => (
    <Input
      value={value || ''}
      onChange={(e) => onChange(record, 'remarks', e.target.value)}
      placeholder="请输入备注信息"
    />
  ),
)

RemarkInput.displayName = 'RemarkInput'

const useStyle = createStyles(({ css }) => {
  const antCls = '.ant'
  return {
    customTable: css`
      ${antCls}-table-thead > tr > th {
        background-color: #e5ebfe;
        font-weight: 400;
      }
    `,
  }
})

export const AreaUpdate = ({
  data: details,
}: {
  data: {
    investment_year: string
    approval_node_status: number
    company_name: string
    status: number
  }
}) => {
  const { styles } = useStyle()
  const [messageApi, contextHolder] = message.useMessage()
  const { id } = useSearch({ strict: false }) as { id: string }
  const { tableName } = useParams({ strict: false })
  const [tableData, setTableData] = useState<Area[]>([])

  // 排序区域数据
  const sortAreaData = useCallback((data: Area[]): Area[] => {
    const areaIndexMap = Object.fromEntries(
      AREA_SORT.map((area, index) => [area, index]),
    )

    return [...data].sort((a, b) => {
      const indexA = areaIndexMap[a.style] ?? AREA_SORT.length
      const indexB = areaIndexMap[b.style] ?? AREA_SORT.length
      return indexA - indexB
    })
  }, [])

  // 保存数据
  const { mutate: saveMutate, isPending: saveIsPending } = useMutation({
    mutationFn: async (params: { Items: Area[] }) => {
      const res = await request('/aggregate/plan/area', {
        method: 'PUT',
        body: params,
      })
      return res
    },
    onSuccess: (res) => {
      if (res.code === 200001) {
        messageApi.success('保存成功')
      } else {
        messageApi.error(res?.message)
      }
    },
    onError: (err) => messageApi.error(JSON.stringify(err)),
  })

  // 获取初始数据
  const { data, isLoading } = useQuery({
    queryKey: ['/aggregate/plan/area', id],
    queryFn: async ({ queryKey: [url, id] }) => {
      const response = await request<APIResponse<Area[]>>(url as string, {
        query: { plan_summary_company_id: id },
      })
      if (response.code !== 200001) return null
      return response?.data ?? null
    },
    staleTime: 0,
    retry: false,
    enabled: !!id,
  })

  // 计算规则配置 - 集中定义所有计算依赖关系
  const calculationRules = useMemo(
    () => [
      // 规则1：目标style的字段由依赖style的对应字段累加（如style0000=除0104/0120外的所有字段）
      {
        targetStyle: '0000',
        excludeStyles: EXCLUDE_AREA,
        fields: ['fixed_assets', 'equity_investment'] as CalculableAreaField[],
        type: 'sourceSum' as const,
      },
    ],
    [],
  )

  // 通用计算函数：根据配置规则计算目标字段
  const calculateByRule = useCallback(
    (data: Area[], rule: (typeof calculationRules)[number]): Area[] => {
      const newData = [...data.map((item) => ({ ...item }))]

      if (rule.type === 'sourceSum') {
        const targetRecord = newData.find(
          (item) => item.style === rule.targetStyle,
        )
        if (!targetRecord) return newData

        // 获取所有依赖的源记录
        const sourceRecords = newData.filter(
          (item) => !rule.excludeStyles.includes(item.style),
        )
        // 1. 计算指定字段：累加源记录的对应字段
        rule.fields.forEach((field) => {
          targetRecord[field] = sourceRecords.reduce(
            (sum, item) => addNumbers(sum, item[field]),
            0,
          )
        })
      }

      return newData
    },
    [],
  )

  // 核心计算函数 - 按依赖顺序计算所有字段
  const calculateSums = useCallback(
    (data: Area[]) => {
      let newData = [...data.map((item) => ({ ...item }))]
      calculationRules
        .filter((rule) => rule.type === 'sourceSum')
        .forEach((rule) => {
          newData = calculateByRule(newData, rule)
        })

      return newData
    },
    [calculateByRule, calculationRules],
  )

  // 初始化表格数据
  useEffect(() => {
    if (data && data?.length) {
      // 先排序
      const sortedData = sortAreaData(data)
      // 初始化时执行一次计算
      //const initialData = calculateSums(sortedData)
      const initialData = sortedData
      setTableData(initialData)
    }
  }, [data, sortAreaData, calculateSums])

  const handleInputChange = useCallback(
    (record: Area, field: string, value: number | string) => {
      setTableData((prevData) => {
        // 使用函数式更新
        const newData = prevData.map((item) =>
          item.style === record.style ? { ...item, [field]: value } : item,
        )
        return calculateSums(newData)
      })
    },
    [calculateSums],
  )

  // 备注输入框修改
  const handleRemarkInputChange = useCallback(
    (record: Area, field: string, value: number | string) => {
      setTableData((prevData) => {
        return prevData.map((item) =>
          item.style === record.style ? { ...item, [field]: value } : item,
        )
      })
    },
    [],
  )
  // 处理表单提交
  const handleSubmit = useCallback(() => {
    saveMutate({ Items: tableData })
  }, [saveMutate, tableData])

  // 表格列配置
  const columns: TableColumnsType<Area> = useMemo(() => {
    // const isDisabled = isConsolidatedReport(summaryData.consolidation)
    const isDisabled = true
    return [
      {
        title: '区域',
        dataIndex: 'style',
        key: 'style',
        width: 150,
        align: 'center',
        fixed: 'left',
        render: (value) => {
          if (IMPORTANT_AREA.includes(value)) {
            return (
              <span>
                <em className="text-[#FF4C4C]">*</em>
                {value}
              </span>
            )
          } else if (INCLUDE_AREA.includes(value)) {
            return <span className="text-[#266EFF]">{value}</span>
          } else {
            return value === '0000' ? '合计' : value
          }
        },
      },
      {
        title: '固定资产投资计划额',
        dataIndex: 'fixed_assets',
        key: 'fixed_assets',
        minWidth: 150,
        align: 'center',
        render: (value, record) => (
          <NumberInput
            value={value}
            record={record}
            field="fixed_assets"
            disabled={isDisabled}
            onChange={handleInputChange}
          />
        ),
      },
      {
        title: '股权投资计划额',
        dataIndex: 'equity_investment',
        key: 'equity_investment',
        minWidth: 150,
        align: 'center',
        render: (value, record) => (
          <NumberInput
            value={value}
            record={record}
            field="equity_investment"
            disabled={isDisabled}
            onChange={handleInputChange}
          />
        ),
      },

      {
        title: '备注',
        dataIndex: 'remarks',
        key: 'remarks',
        minWidth: 320,
        align: 'center',
        render: (value, record) => {
          if (record.style === '0000') {
            return (
              <span className="text-[#FF4C4C]">合计数已扣减两个"其中"</span>
            )
          } else {
            return (
              <RemarkInput
                value={value || ''}
                record={record}
                onChange={handleRemarkInputChange}
              />
            )
          }
        },
      },
    ]
  }, [handleInputChange, handleRemarkInputChange])

  const title =
    InvestmentPlanTableNamesMap[
      tableName as keyof typeof InvestmentPlanTableNamesMap
    ]
  const router = useRouter()
  return (
    <div className="flex h-full flex-col">
      {contextHolder}

      <Card title={title}>
        <div className="mb-4 flex items-center justify-between">
          <p>填报年份：{details?.investment_year}</p>
          <p>编制单位：{details?.company_name}</p>
          <p>金额：万元</p>
        </div>
        <Table
          size="small"
          tableLayout="auto"
          bordered
          className={styles.customTable}
          dataSource={tableData}
          columns={columns}
          loading={isLoading || saveIsPending}
          pagination={false}
          scroll={{ x: 'max-content' }}
          sticky={{ offsetHeader: 48 }}
          rowKey="style"
        />
        <div className="py-5">
          <p>注：1.投资项目区域根据项目实施所在地确定。</p>
          <p className="ml-7">
            2.跨省（自治区、直辖市）开展的投资项目根据实际情况可按跨区域投资统计。
          </p>
          <p className="ml-7 text-[#FF4C4C]">
            3.珠三角九市包括广州、深圳、珠海、佛山、惠州、东莞、中山、江门、肇庆。
          </p>
        </div>
        <div className="sticky bottom-0 flex justify-end gap-2 bg-white py-2">
          {details?.approval_node_status !== INVESTMENT_STATUS.REPORTED &&
            details?.status === 2 && (
              <Button
                type="primary"
                onClick={handleSubmit}
                loading={saveIsPending}
              >
                保存数据
              </Button>
            )}
          <Button onClick={() => router.history.back()}>返回</Button>
        </div>
      </Card>
    </div>
  )
}
