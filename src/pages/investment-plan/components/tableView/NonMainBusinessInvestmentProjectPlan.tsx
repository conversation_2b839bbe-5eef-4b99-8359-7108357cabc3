import { useQuery } from '@tanstack/react-query'
import { usePara<PERSON>, useRouter } from '@tanstack/react-router'
import { Card, Button, Table, Typography, type TableColumnsType } from 'antd'
import { createStyles } from 'antd-style'
import numeral from 'numeral'
import { useMemo } from 'react'

import { type APIResponse, request } from '@/lib/request.ts'
import {
  INDUSTRY_TYPE,
  SPECIAL_CONSIDERATION_TYPE,
} from '@/universal/basic-form/constants.ts'

import { InvestmentPlanTableNamesMap } from '../../constants.ts'

import type { NonMainBusinessInvestmentProjectPlan } from './types.ts'

const useStyle = createStyles(({ css }) => {
  const antCls = '.ant'
  return {
    customTable: css`
      ${antCls}-table-thead > tr > th {
        background-color: #e5ebfe;
        font-weight: 400;
      }
    `,
  }
})

export const NonMainBusinessInvestmentProjectPlanView = ({
  data: details,
}: {
  data: {
    investment_year: string
    company_id: string
    company_name: string
    consolidation: string
  }
}) => {
  const { styles } = useStyle()
  const { tableName } = useParams({ strict: false })
  const getUrl = '/aggregate/plan/non-main-project'

  // 获取初始数据
  const { data, isLoading } = useQuery({
    queryKey: [getUrl, details] as const,
    queryFn: async ({ queryKey: [url, details] }) => {
      const response = await request<
        APIResponse<NonMainBusinessInvestmentProjectPlan[]>
      >(url as string, {
        query: {
          belong_company_id: details.company_id,
          year: details.investment_year,
          consolidation: details.consolidation,
        },
      })
      if (response.code !== 200001) return null
      return response?.data ?? null
    },
    staleTime: 0,
    retry: false,
    enabled: !!details?.investment_year && !!details?.company_id,
  })

  // 表格列配置
  const columns: TableColumnsType<NonMainBusinessInvestmentProjectPlan> =
    useMemo(() => {
      return [
        {
          title: '序号',
          align: 'center',
          width: 60,
          render: (_, __, index) => {
            return index + 1
          },
        },
        {
          title: '项目名称',
          dataIndex: 'project_name',
          minWidth: 160,
          ellipsis: {
            showTitle: false,
          },
          render: (value) => (
            <Typography.Text ellipsis={{ tooltip: value }}>
              {value}
            </Typography.Text>
          ),
        },
        {
          title: '固资/股权',
          dataIndex: 'is_real_estate_investment',
          width: 120,
          render: (value) => {
            return value === 1 ? '固定资产投资' : '股权投资'
          },
        },
        {
          title: '项目地点',
          dataIndex: 'project_area',
          width: 150,
        },
        {
          title: '项目内容',
          dataIndex: 'project_content',
          minWidth: 160,
          ellipsis: {
            showTitle: false,
          },
          render: (value) => (
            <Typography.Text ellipsis={{ tooltip: value }}>
              {value}
            </Typography.Text>
          ),
        },
        {
          title: '所属行业/被投资企业所属行业',
          dataIndex: 'industry_type',
          minWidth: 100,
          ellipsis: {
            showTitle: false,
          },
          render: (value) => {
            return (
              INDUSTRY_TYPE.find((item) => item.value === value)?.label + value
            )
          },
        },
        {
          title: '项目计划总投资（万元）',
          dataIndex: 'project_plan_total_investment',
          minWidth: 180,
          align: 'right',
          ellipsis: {
            showTitle: false,
          },
          render: (value) => (
            <Typography.Text ellipsis={{ tooltip: value }}>
              {numeral(value).format('0,0.00')}
            </Typography.Text>
          ),
        },
        {
          title: '资金来源-自有资金（万元）',
          dataIndex: 'fund_source_self',
          minWidth: 180,
          align: 'right',
          ellipsis: {
            showTitle: false,
          },
          render: (value) => (
            <Typography.Text ellipsis={{ tooltip: value }}>
              {numeral(value).format('0,0.00')}
            </Typography.Text>
          ),
        },
        {
          title: '资金来源-贷款（万元）',
          dataIndex: 'fund_source_loan',
          minWidth: 180,
          align: 'right',
          ellipsis: {
            showTitle: false,
          },
          render: (value) => (
            <Typography.Text ellipsis={{ tooltip: value }}>
              {numeral(value).format('0,0.00')}
            </Typography.Text>
          ),
        },
        {
          title: '资金来源-其它（万元）',
          dataIndex: 'fund_source_other',
          minWidth: 180,
          align: 'right',
          ellipsis: {
            showTitle: false,
          },
          render: (value) => (
            <Typography.Text ellipsis={{ tooltip: value }}>
              {numeral(value).format('0,0.00')}
            </Typography.Text>
          ),
        },
        {
          title: '本年度计划投资（万元）',
          dataIndex: 'plan_investment_cur_year',
          width: 200,
          align: 'right',
          ellipsis: {
            showTitle: false,
          },
          render: (value: string) => (
            <Typography.Text ellipsis={{ tooltip: value }}>
              {numeral(value).format('0,0.00')}
            </Typography.Text>
          ),
        },
        {
          title: '项目投资必要性',
          dataIndex: 'investment_necessity',
          minWidth: 160,
          ellipsis: {
            showTitle: false,
          },
          render: (value) => (
            <Typography.Text ellipsis={{ tooltip: value }}>
              {value}
            </Typography.Text>
          ),
        },

        {
          title: '特殊考虑事项类型',
          dataIndex: 'special_consideration_type',
          width: 160,
          ellipsis: {
            showTitle: false,
          },
          render: (value) => {
            return SPECIAL_CONSIDERATION_TYPE.find(
              (item) => item.value === value,
            )?.label
          },
        },
        {
          title: '备注',
          dataIndex: 'remarks',
          minWidth: 200,
          ellipsis: {
            showTitle: false,
          },
          render: (value) => (
            <Typography.Text ellipsis={{ tooltip: value }}>
              {value}
            </Typography.Text>
          ),
        },
      ]
    }, [])

  const title =
    InvestmentPlanTableNamesMap[
      tableName as keyof typeof InvestmentPlanTableNamesMap
    ]
  const router = useRouter()

  return (
    <div className="flex h-full flex-col">
      <Card title={title}>
        <div className="mb-4 flex items-center justify-between">
          <p>填报年份：{details?.investment_year}</p>
          <p>编制单位：{details?.company_name}</p>
          <p>金额：万元</p>
        </div>
        <Table
          size="small"
          tableLayout="auto"
          bordered
          className={styles.customTable}
          dataSource={data || []}
          loading={isLoading}
          columns={columns}
          scroll={{ x: 'max-content' }}
          sticky={{ offsetHeader: 48 }}
          pagination={false}
          rowKey="approval_id"
        />
        <div className="sticky bottom-0 flex justify-end gap-2 bg-white py-2">
          <Button onClick={() => router.history.back()}>返回</Button>
        </div>
      </Card>
    </div>
  )
}
