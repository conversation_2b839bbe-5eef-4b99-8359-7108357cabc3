import { useQuery } from '@tanstack/react-query'
import { useP<PERSON><PERSON>, useRouter } from '@tanstack/react-router'
import { Card, Button, Table, Typography, type TableColumnsType } from 'antd'
import { createStyles } from 'antd-style'
import numeral from 'numeral'
import { useMemo } from 'react'

import { type APIResponse, request } from '@/lib/request.ts'
import { getIndustryNewTypePath } from '@/universal/basic-form'
import {
  INDUSTRY_TYPE,
  INVESTOR_TYPE_OPTIONS,
  PROJECT_CATEGORY,
} from '@/universal/basic-form/constants.ts'

import { InvestmentPlanTableNamesMap } from '../../constants.ts'

import type { EquityProject } from './types.ts'

const useStyle = createStyles(({ css }) => {
  const antCls = '.ant'
  return {
    customTable: css`
      ${antCls}-table-thead > tr > th {
        background-color: #e5ebfe;
        font-weight: 400;
      }
    `,
  }
})

export const EquityProjectView = ({
  page = 'PLAN',
  data: details,
}: {
  page?: 'PLAN' | 'COMPLETION'
  data?: {
    investment_year: string
    company_id: string
    company_name: string
    consolidation: string
    period: string
  }
}) => {
  const { styles } = useStyle()
  const { tableName } = useParams({ strict: false })
  const getUrl =
    page === 'PLAN'
      ? '/aggregate/plan/equity-investment'
      : '/aggregate/completed/equity-investment'

  // 获取初始数据
  const { data, isLoading } = useQuery({
    queryKey: [getUrl, details] as const,
    queryFn: async ({ queryKey: [url, details] }) => {
      const response = await request<
        APIResponse<{
          Total: number
          Data: EquityProject[]
        }>
      >(url as string, {
        query: {
          belong_company_id: details?.company_id,
          year: details?.investment_year,
          consolidation: details?.consolidation,
          period: details?.period,
        },
      })
      if (response.code !== 200001) return null
      return response?.data ?? null
    },
    staleTime: 0,
    retry: false,
    enabled: !!details?.investment_year && !!details?.company_id,
  })

  // 表格列配置
  const columns: TableColumnsType<EquityProject> = useMemo(() => {
    return [
      {
        title: '序号',
        align: 'center',
        width: 60,
        render: (_, __, index) => {
          return index + 1
        },
      },
      {
        title: '项目名称',
        dataIndex: 'project_name',
        width: 160,
        ellipsis: {
          showTitle: false,
        },
        render: (value) => (
          <Typography.Text ellipsis={{ tooltip: value }}>
            {value}
          </Typography.Text>
        ),
      },
      {
        title: '投资分类',
        dataIndex: 'investor_type',
        width: 100,
        render: (value) => {
          return INVESTOR_TYPE_OPTIONS.find((item) => item.value === value)
            ?.label
        },
      },
      {
        title: '项目地点',
        dataIndex: 'project_area',
        width: 150,
      },
      {
        title: '项目内容',
        dataIndex: 'project_content',
        width: 160,
        ellipsis: {
          showTitle: false,
        },
        render: (value) => (
          <Typography.Text ellipsis={{ tooltip: value }}>
            {value}
          </Typography.Text>
        ),
      },
      ...(page === 'PLAN'
        ? [
            {
              title: '预期实现效果、主要风险及应对举措',
              dataIndex: 'project_expect',
              width: 250,
              ellipsis: {
                showTitle: false,
              },
              render: (value: string) => (
                <Typography.Text ellipsis={{ tooltip: value }}>
                  {value}
                </Typography.Text>
              ),
            },
          ]
        : [
            {
              title: '项目进度描述',
              dataIndex: 'project_progress_desc',
              width: 250,
              ellipsis: {
                showTitle: false,
              },
              render: (value: string) => (
                <Typography.Text ellipsis={{ tooltip: value }}>
                  {value}
                </Typography.Text>
              ),
            },
          ]),

      {
        title: '项目分类',
        dataIndex: 'project_category',
        ellipsis: {
          showTitle: false,
        },
        width: 200,
        render: (value) => {
          const categories = value
            ?.split(',')
            .map(
              (item: string) =>
                PROJECT_CATEGORY.find((i) => i.value === item)?.label + ',',
            )
          return (
            <Typography.Text ellipsis={{ tooltip: categories }}>
              {categories}
            </Typography.Text>
          )
        },
      },
      {
        title: '所属行业',
        dataIndex: 'industry_type',
        width: 120,
        ellipsis: {
          showTitle: false,
        },
        render: (value) => {
          const industryType = INDUSTRY_TYPE.find(
            (item) => item.value === value,
          )
          return (
            <Typography.Text ellipsis={{ tooltip: industryType?.label }}>
              {industryType?.label}
            </Typography.Text>
          )
        },
      },
      {
        title: '所属战新产业',
        dataIndex: 'industry_new_type',
        width: 200,
        ellipsis: {
          showTitle: false,
        },
        render: (value) => {
          const industryNewType = getIndustryNewTypePath(value).join('/')
          return (
            <Typography.Text ellipsis={{ tooltip: industryNewType }}>
              {industryNewType}
            </Typography.Text>
          )
        },
      },
      {
        title: '项目计划总投资（万元）',
        dataIndex: 'project_plan_total_investment',
        width: 180,
        align: 'right',
        ellipsis: {
          showTitle: false,
        },
        render: (value) => (
          <Typography.Text ellipsis={{ tooltip: value }}>
            {numeral(value).format('0,0.00')}
          </Typography.Text>
        ),
      },
      {
        title: '截止上年底完成投资',
        dataIndex: 'investmentcompleted_last_year',
        width: 180,
        align: 'right',
        ellipsis: {
          showTitle: false,
        },
        render: (value) => (
          <Typography.Text ellipsis={{ tooltip: value }}>
            {numeral(value).format('0,0.00')}
          </Typography.Text>
        ),
      },
      ...(page === 'PLAN'
        ? [
            {
              title: '本年度计划投资（万元）',
              dataIndex: 'plan_investment_cur_year',
              width: 200,
              align: 'right' as const,
              ellipsis: {
                showTitle: false,
              },
              render: (value: number) => (
                <Typography.Text ellipsis={{ tooltip: value }}>
                  {numeral(value).format('0,0.00')}
                </Typography.Text>
              ),
            },
          ]
        : [
            {
              title: '本年度完成投资（万元）',
              dataIndex: 'investment_completed_cur_year',
              width: 200,
              align: 'right' as const,
              ellipsis: {
                showTitle: false,
              },
              render: (value: number) => (
                <Typography.Text ellipsis={{ tooltip: value }}>
                  {numeral(value).format('0,0.00')}
                </Typography.Text>
              ),
            },
          ]),

      {
        title: '投资完成后所占股比',
        dataIndex: 'equity_ratio_after_investmen',
        width: 180,
        align: 'right',
        render: (text) => text + '%',
      },

      ...(page === 'PLAN'
        ? [
            {
              title: '预计投资收益率',
              dataIndex: 'investment_return_rate_expect',
              width: 120,
              align: 'right' as const,
              render: (text: number) => text + '%',
            },
          ]
        : []),

      {
        title: '其他股东及股比情况',
        dataIndex: 'shareholders_info',
        width: 140,
      },
      {
        title: '备注',
        dataIndex: page === 'PLAN' ? 'remarks' : 'completed_remarks',
        width: 200,
        ellipsis: {
          showTitle: false,
        },
        render: (value) => (
          <Typography.Text ellipsis={{ tooltip: value }}>
            {value}
          </Typography.Text>
        ),
      },
    ]
  }, [page])

  const planTitle =
    InvestmentPlanTableNamesMap[
      tableName as keyof typeof InvestmentPlanTableNamesMap
    ]
  const title = page === 'PLAN' ? planTitle : planTitle.replace('计划', '完成')
  const router = useRouter()

  return (
    <div className="flex h-full flex-col">
      <Card title={title}>
        <div className="mb-4 flex items-center justify-between">
          <p>填报年份：{details?.investment_year}</p>
          <p>编制单位：{details?.company_name}</p>
          <p>金额：万元</p>
        </div>
        <Table
          size="small"
          bordered
          className={styles.customTable}
          dataSource={data?.Data ?? []}
          loading={isLoading}
          columns={columns}
          scroll={{ x: '100%' }}
          sticky={{ offsetHeader: 48 }}
          pagination={false}
          rowKey="approval_id"
        />
        <div className="sticky bottom-0 flex justify-end gap-2 bg-white py-2">
          <Button onClick={() => router.history.back()}>返回</Button>
        </div>
      </Card>
    </div>
  )
}
