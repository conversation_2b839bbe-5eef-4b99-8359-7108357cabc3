export interface FixedAssetProject {
  project_id: string
  project_status: number
  project_op_uid: string
  project_op_name: string
  project_op_company_id: string
  investment_year: string
  company_id: string
  company_name: string
  investor_type: number
  project_name: string
  project_area: string
  project_category: string
  project_content: string
  project_expect: string
  industry_type: string
  industry_new_type: string
  is_plan_investment: number
  is_real_estate_investment: number
  project_plan_total_investment: number
  investmentcompleted_last_year: number
  plan_investment_cur_year: number
  start_time: string
  complete_time_expect: string
  investment_return_rate_expect: number
  has_equipment_upgrade: number
  equipment_upgrade_amount: number
  is_key_project: number
  project_phase: number
  special_consideration_type: number
  special_total_investment: number
  investment_necessity: string
  fund_source_self: number
  fund_source_loan: number
  fund_source_other: number
  remarks: string
  version_id: string
  project_progress_desc: string
  investment_completed_cur_year: number
  completed_fund_source_self: number
  completed_fund_source_loan: number
  completed_fund_source_other: number
  actual_start_date: string
  funds_received: number
  completed_remarks: string
  reprot_month: string
  approval_id: string
  approval_status: number
  reject_reason: string
  approval_node_id: string
  approval_node_status: number
  prev_node_id: string
  next_node_id: string
  approval_update_at: string
  creator_id: string
  creator_name: string
  modify_id: string
  modify_name: string
  op_uid: string
  op_name: string
  reject_view: number
  delete_view: number
  project_status_in_progress_view: number
  project_status_aborted_view: number
  project_status_completed_view: number
  lock_data: number
}

export interface EquityProject {
  project_id: string
  project_status: number
  project_op_uid: string
  project_op_name: string
  project_op_company_id: string
  investment_year: string
  company_id: string
  company_name: string
  investor_type: number
  project_name: string
  project_area: string
  project_category: string
  project_content: string
  project_expect: string
  industry_type: string
  industry_new_type: string
  is_plan_investment: number
  is_real_estate_investment: number
  project_plan_total_investment: number
  investmentcompleted_last_year: number
  plan_investment_cur_year: number
  start_time: string
  complete_time_expect: string
  investment_return_rate_expect: number
  has_equipment_upgrade: number
  equipment_upgrade_amount: number
  is_key_project: number
  project_phase: number
  special_consideration_type: number
  special_total_investment: number
  investment_necessity: string
  fund_source_self: number
  fund_source_loan: number
  fund_source_other: number
  remarks: string
  version_id: string
  project_progress_desc: string
  investment_completed_cur_year: number
  completed_fund_source_self: number
  completed_fund_source_loan: number
  completed_fund_source_other: number
  actual_start_date: string
  funds_received: number
  completed_remarks: string
  reprot_month: string
  approval_id: string
  approval_status: number
  reject_reason: string
  approval_node_id: string
  approval_node_status: number
  prev_node_id: string
  next_node_id: string
  approval_update_at: string
  creator_id: string
  creator_name: string
  modify_id: string
  modify_name: string
  op_uid: string
  op_name: string
  reject_view: number
  delete_view: number
  project_status_in_progress_view: number
  project_status_aborted_view: number
  project_status_completed_view: number
}

export interface PostEvaluation {
  project_id: string
  project_status: number
  project_op_uid: string
  project_op_name: string
  project_op_company_id: string
  investment_year: string
  company_id: string
  company_name: string
  investor_type: number
  project_name: string
  project_area: string
  project_category: string
  project_content: string
  project_expect: string
  industry_type: string
  industry_new_type: string
  is_plan_investment: number
  is_real_estate_investment: number
  project_plan_total_investment: number
  investmentcompleted_last_year: number
  plan_investment_cur_year: number
  start_time: string
  complete_time_expect: string
  investment_return_rate_expect: number
  has_equipment_upgrade: number
  equipment_upgrade_amount: number
  is_key_project: number
  project_phase: number
  special_consideration_type: number
  special_total_investment: number
  investment_necessity: string
  fund_source_self: number
  fund_source_loan: number
  fund_source_other: number
  remarks: string
  version_id: string
  project_progress_desc: string
  investment_completed_cur_year: number
  completed_fund_source_self: number
  completed_fund_source_loan: number
  completed_fund_source_other: number
  actual_start_date: string
  funds_received: number
  completed_remarks: string
  reprot_month: string
  approval_id: string
  approval_status: number
  reject_reason: string
  approval_node_id: string
  approval_node_status: number
  prev_node_id: string
  next_node_id: string
  approval_update_at: string
  creator_id: string
  creator_name: string
  modify_id: string
  modify_name: string
  op_uid: string
  op_name: string
  reject_view: number
  delete_view: number
  project_status_in_progress_view: number
  project_status_aborted_view: number
  project_status_completed_view: number
}

export interface NonMainBusinessInvestmentProjectPlan {
  project_id: string
  project_status: number
  project_op_uid: string
  project_op_name: string
  project_op_company_id: string
  investment_year: string
  company_id: string
  company_name: string
  investor_type: number
  project_name: string
  project_area: string
  project_category: string
  project_content: string
  project_expect: string
  industry_type: string
  industry_new_type: string
  is_plan_investment: number
  is_real_estate_investment: number
  project_plan_total_investment: number
  investmentcompleted_last_year: number
  plan_investment_cur_year: number
  start_time: string
  complete_time_expect: string
  investment_return_rate_expect: number
  has_equipment_upgrade: number
  equipment_upgrade_amount: number
  is_key_project: number
  project_phase: number
  special_consideration_type: number
  special_total_investment: number
  investment_necessity: string
  fund_source_self: number
  fund_source_loan: number
  fund_source_other: number
  remarks: string
  version_id: string
  project_progress_desc: string
  investment_completed_cur_year: number
  completed_fund_source_self: number
  completed_fund_source_loan: number
  completed_fund_source_other: number
  actual_start_date: string
  funds_received: number
  completed_remarks: string
  reprot_month: string
  approval_id: string
  approval_status: number
  reject_reason: string
  approval_node_id: string
  approval_node_status: number
  prev_node_id: string
  next_node_id: string
  approval_update_at: string
  creator_id: string
  creator_name: string
  modify_id: string
  modify_name: string
  op_uid: string
  op_name: string
  reject_view: number
  delete_view: number
  project_status_in_progress_view: number
  project_status_aborted_view: number
  project_status_completed_view: number
}
