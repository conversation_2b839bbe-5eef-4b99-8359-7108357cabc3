import { SyncOutlined } from '@ant-design/icons'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { Button, Modal, Result } from 'antd'
import { useState, useEffect } from 'react'

import { type APIResponse, request } from '@/lib/request.ts'

export const ReportModal = ({
  isOpen = false,
  selectedKeys,
  year,
  onClose,
}: {
  isOpen: boolean
  selectedKeys: string[]
  year: string
  onClose: () => void // 必传：通知父组件关闭弹窗
}) => {
  const queryClient = useQueryClient()
  const [isModalOpen, setIsModalOpen] = useState(isOpen)

  const [reportStatus, setReportStatus] = useState<
    'init' | 'loading' | 'error' | 'success'
  >('init')
  const [reportMessage, setReportMessage] = useState('')

  useEffect(() => {
    setIsModalOpen(isOpen)

    if (!isOpen) {
      setReportStatus('init')
      setReportMessage('')
    }
  }, [isOpen])

  const handleClose = () => {
    setIsModalOpen(false)
    onClose()
  }
  const STATUS_CARD = {
    init: <div className="mt-6">上报周期: {year}年</div>,
    loading: (
      <Result
        title="上报中..."
        icon={<SyncOutlined spin />}
        extra={
          <Button
            onClick={() => {
              queryClient.cancelQueries({ queryKey: ['report'] })
              handleClose()
            }}
          >
            取消
          </Button>
        }
      />
    ),
    error: (
      <Result
        title="上报失败"
        status="error"
        subTitle={'报错: ' + reportMessage}
        extra={
          <Button onClick={handleClose} type="primary">
            确定
          </Button>
        }
      />
    ),
    success: (
      <Result
        title="上报成功"
        status="success"
        extra={
          <Button onClick={handleClose} type="primary">
            确定
          </Button>
        }
      />
    ),
  }

  const handleReport = useMutation({
    mutationKey: ['report'],
    mutationFn: async () => {
      setReportStatus('loading')

      return await request<APIResponse<null>>('/approval/pending-mul', {
        method: 'POST',
        body: { node_ids: selectedKeys },
      })
    },

    onError: (err) => {
      setReportStatus('error')
      setReportMessage(JSON.stringify(err))
    },
    onSuccess(res) {
      if (res.code !== 200001) {
        setReportStatus('error')
        setReportMessage(res.message)
        return
      }
      setReportStatus('success')
    },
  })

  return (
    <Modal
      title="上报国资委"
      width={420}
      styles={{
        body: {
          minHeight: 200,
        },
      }}
      maskClosable={false}
      okText="下一步"
      open={isModalOpen}
      onCancel={handleClose}
      onOk={() => {
        handleReport.mutate()
      }}
      footer={reportStatus === 'init' ? undefined : null}
    >
      {STATUS_CARD[reportStatus]}
    </Modal>
  )
}
