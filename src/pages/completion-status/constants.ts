import { EquityProjectView } from '@/pages/investment-plan/components/tableView/EquityProject'
import { FixedAssetProjectPlanView } from '@/pages/investment-plan/components/tableView/FixedAssetProject'
import { PostEvaluationView } from '@/pages/investment-plan/components/tableView/PostEvaluation'

import { AreaUpdate } from './components/tableUpdate/AreaUpdate'
import { CompletionTotalUpdate } from './components/tableUpdate/CompletionTotalUpdate'
import { IndustrySetFormulaUpdate } from './components/tableUpdate/IndustrySetFormulaUpdate'
import { StrategicNewUpdate } from './components/tableUpdate/StrategicNewUpdate'

/** 完成状况相关表名 */
export const CompletionStatusTable = {
  /** 投资情况完成总表 */
  CompletionTotal: 'completion-total',
  /** 战略性新兴产业投资完成情况表*/
  StrategicNew: 'strategic-new',
  /** 国民经济行业分类投资完成情况表 */
  IndustrySetFormula: 'industry-set-formula',
  /** 区域分布投资完成情况表 */
  Area: 'area',
  /** 固定资产投资项目完成情况表 */
  FixedAssetProject: 'fixed-asset-project',
  /** 股权投资项目完成情况表 */
  EquityProject: 'equity-project',
  /** 投资后评价项目完成情况表 */
  PostEvaluation: 'post-evaluation',
  /** 补充附件 */
  // SupplementAttachment: 'supplement-attachment',
} as const

/** 投资完成状况相关表名 */
export const CompletionStatusTableNamesMap = {
  [CompletionStatusTable.CompletionTotal]: '投资情况完成总表',
  [CompletionStatusTable.StrategicNew]: '战略性新兴产业投资完成情况表',
  [CompletionStatusTable.IndustrySetFormula]: '国民经济行业分类投资完成情况表',
  [CompletionStatusTable.Area]: '区域分布投资完成情况表',
  [CompletionStatusTable.FixedAssetProject]: '固定资产投资项目完成情况表',
  [CompletionStatusTable.EquityProject]: '股权投资项目完成情况表',
  [CompletionStatusTable.PostEvaluation]: '投资后评价项目完成情况表',
  // [CompletionStatusTable.SupplementAttachment]: '补充附件',
}

/** 投资完成状况更新页面对应组件 */
export const CompletionStatusTableUpdateMap = {
  [CompletionStatusTable.CompletionTotal]: CompletionTotalUpdate,
  [CompletionStatusTable.StrategicNew]: StrategicNewUpdate,
  [CompletionStatusTable.IndustrySetFormula]: IndustrySetFormulaUpdate,
  [CompletionStatusTable.Area]: AreaUpdate,
  [CompletionStatusTable.FixedAssetProject]: FixedAssetProjectPlanView,
  [CompletionStatusTable.EquityProject]: EquityProjectView,
  [CompletionStatusTable.PostEvaluation]: PostEvaluationView,
} as const
