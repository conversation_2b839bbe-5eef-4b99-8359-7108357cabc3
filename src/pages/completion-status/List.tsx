import { useQuery } from '@tanstack/react-query'
import { Link, useNavigate, useSearch } from '@tanstack/react-router'
import { Button, Card, Table, type TableColumnsType } from 'antd'
import { CalendarClockIcon } from 'lucide-react'
import numeral from 'numeral'
import { useMemo } from 'react'

import SkeletonTable, {
  type SkeletonTableColumnsType,
} from '@/components/SkeletonTable'
import { convertToQuarter } from '@/lib/dateUtils'
import { type APIResponse, request } from '@/lib/request'

import { CompletionStatusTableNamesMap } from './constants'
import type { CompletionTotalPageDTO } from './types'

export function CompletionStatusList() {
  const navigate = useNavigate()
  const { id, company_name, approval_node_id } = useSearch({
    strict: false,
  }) as {
    id: string
    company_name: string
    approval_node_id: number
  }

  const { data: statisticalData } = useQuery({
    queryKey: ['/aggregate/completed/detail-by-id', id] as const,
    queryFn: async ({ queryKey: [url, id] }) => {
      const response = await request<APIResponse<CompletionTotalPageDTO>>(
        url as string,
        {
          query: { id },
        },
      )
      if (response.code !== 200001) return null
      return response?.data
    },
    staleTime: 0,
    retry: false,
    enabled: !!id,
  })

  const tableData = useMemo(() => {
    return Object.entries(CompletionStatusTableNamesMap).map(
      ([key, value]) => ({
        label: value,
        value: key,
      }),
    )
  }, [])

  const columns = useMemo(() => {
    return [
      {
        title: '序号',
        align: 'center',
        width: 60,
        render: (_, __, index) => {
          return index + 1
        },
      },
      {
        title: '表单名称',
        dataIndex: 'label',
        minWidth: 160,
        render: (label, record) => (
          <Link
            to="/data-summary/completion-status/table/$tableName"
            search={{
              id,
              company_name,
              approval_node_id,
            }}
            params={{ tableName: record.value }}
          >
            {label}
          </Link>
        ),
      },
    ] as TableColumnsType<(typeof tableData)[number]>
  }, [id, company_name, approval_node_id])

  const { year, quarter } = useMemo(() => {
    return statisticalData?.period
      ? convertToQuarter(statisticalData.period)
      : { year: '', quarter: '' }
  }, [statisticalData?.period])

  return (
    <div className="flex h-full flex-col gap-4">
      <Button
        className="-my-4 self-start"
        type="link"
        onClick={() =>
          navigate({
            to: '/data-summary/completion-status',
          })
        }
      >
        {'<< 返回上一页'}
      </Button>
      <Card>
        <div className="space-y-6">
          <div className="flex justify-between">
            <h2 className="text-xl font-semibold">
              {statisticalData?.company_name}
            </h2>
          </div>
          <div className="flex items-center gap-2">
            <CalendarClockIcon className="size-4" />
            <span className="text-sm text-[#666]">本年度完成投资总额：</span>
            <span className="text-xl font-semibold">
              {numeral(statisticalData?.current_year_total).format('0,0.00')}
              万元
            </span>
          </div>
        </div>
      </Card>
      <Card title={`投资完成情况-${year}_Q${quarter}`}>
        <SkeletonTable columns={columns as SkeletonTableColumnsType[]}>
          <Table
            rowKey="value"
            size="small"
            pagination={false}
            dataSource={tableData}
            columns={columns}
            scroll={{ x: 'max-content' }}
            sticky={{ offsetHeader: 48 }}
          />
        </SkeletonTable>
      </Card>
    </div>
  )
}
