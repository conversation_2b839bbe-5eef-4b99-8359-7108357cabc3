export interface ProjectData {
  id?: string
  // 项目基本信息
  project_id: string // 项目ID
  project_name: string // 项目名称
  investment_year: string // 投资年份（格式：YYYY）
  group_company_id: string // 集团公司ID
  group_company_name: string // 集团公司名称
  industry_type: string // 行业类型编码（如："A013"）
  company_id: string // 实施企业ID
  company_name: string // 实施企业名称
  project_area: string // 项目地区（如："西藏"）

  // 项目属性（枚举值，需结合业务文档确认具体含义）
  project_implementation_purpose: number // 项目实施目的
  project_category: number // 项目类别
  has_standard: number // 是否有标准（如：1=有）
  standard_name: number // 标准名称（可能为枚举值）

  // 项目描述信息
  project_construction_content: string // 项目建设内容
  project_expected_outcome: string // 项目预期成效
  remarks: string // 备注信息

  // 时间信息
  planned_operation_time: string // 计划运营时间（格式：YYYY-MM-DD HH:mm:ss）
  actual_start_date: string // 实际开始日期（格式：YYYY-MM-DD HH:mm:ss）

  // 投资金额信息（数值类型，单位根据业务定义）
  plan_investment_cur_year: number // 本年度计划投资额
  project_plan_total_investment: number // 项目计划总投资额
  investment_completed_cur_year: number // 本年度已完成投资额
  src_id: string
}

export interface SelectProjectData {
  // 项目实际开始日期（格式：YYYY-MM-DD）
  actual_start_date: string
  // 公司ID
  company_id: string
  // 公司名称
  company_name: string
  // 行业类型编码
  industry_type: string
  // 本年度已完成投资额
  investment_completed_cur_year: number
  // 投资年份（为空字符串表示未填写）
  investment_year: string
  // 是否为重点项目（0=否，1=是）
  is_key_project: number
  // 本年度计划投资额
  plan_investment_cur_year: number
  // 项目所在地区
  project_area: string
  // 项目内容描述
  project_content: string
  // 项目预期目标
  project_expect: string
  // 项目唯一标识ID
  project_id: string
  // 项目名称
  project_name: string
  // 项目计划总投资额
  project_plan_total_investment: number
}
