import { Tooltip } from 'antd'
import { CircleAlertIcon } from 'lucide-react'
import React from 'react'

type TooltipProps = React.ComponentProps<typeof Tooltip>
type FormTipProps = TooltipProps & {
  textColor?: string
}

const FormTip: React.FC<FormTipProps> = ({
  textColor = '#333',
  color = '#fff',
  children,
  ...props
}) => {
  const displayChildren = children || (
    <CircleAlertIcon className="size-5 text-[#ddd]" />
  )

  return (
    <Tooltip
      styles={{
        body: {
          color: textColor,
        },
      }}
      color={color}
      {...props}
    >
      {displayChildren}
    </Tooltip>
  )
}

export default FormTip
