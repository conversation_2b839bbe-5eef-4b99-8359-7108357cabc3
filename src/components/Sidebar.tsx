import { useLocation, useNavigate } from '@tanstack/react-router'
import { Menu } from 'antd'
import type { ClassValue } from 'clsx'
import { CalendarClockIcon, ChartLineIcon, FileTextIcon } from 'lucide-react'
import { useEffect, useState } from 'react'

import { cn } from '@/lib/utils'

const menuItems = [
  {
    key: 'basic-report',
    label: '基础表单填报',
    icon: <CalendarClockIcon className="!size-4" />,
    children: [
      { key: '/basic-report/fixed-assets', label: '固资项目' },
      { key: '/basic-report/equity-projects', label: '股权项目' },
      { key: '/basic-report/post-evaluation', label: '后评价' },
    ],
  },
  {
    key: 'data-summary',
    label: '数据汇总',
    icon: <FileTextIcon className="!size-4" />,
    children: [
      { key: '/data-summary/investment-plan', label: '投资计划' },
      { key: '/data-summary/monthly-report', label: '投资月报' },
      { key: '/data-summary/completion-status', label: '投资完成情况' },
    ],
  },
  {
    key: 'strategic-report',
    label: '战新报表',
    icon: <ChartLineIcon className="!size-4" />,
    children: [
      { key: '/strategic-report/development-index', label: '发展指数' },
      { key: '/strategic-report/device-update', label: '大规模设备更新' },
    ],
  },
]

export function Sidebar({
  collapsed,
  className,
}: {
  collapsed: boolean
  className?: ClassValue
}) {
  const location = useLocation()
  const navigate = useNavigate()
  const [openKeys, setOpenKeys] = useState<string[]>([])
  const currentPath = location.pathname

  useEffect(() => {
    const newOpenKey = menuItems.find((item) =>
      (item.children || []).some((child) => currentPath.startsWith(child.key)),
    )?.key

    if (newOpenKey) {
      setOpenKeys((prev) => {
        if (prev.includes(newOpenKey)) return prev
        return [...prev, newOpenKey]
      })
    }
  }, [currentPath])

  // 查找选中的菜单项
  const selectedKey = menuItems
    .flatMap((item) => item.children || [])
    .find((child) => currentPath.startsWith(child.key))?.key

  return (
    <aside
      className={cn(
        'fixed top-[48px] bottom-0 left-0 z-50 flex w-[240px] flex-col bg-white shadow-lg transition-[width] duration-300 ease-[cubic-bezier(0.2,0,0,1)]',
        {
          'w-[79px]': collapsed,
        },
        className,
      )}
    >
      <div className="flex-1 overflow-y-auto">
        <Menu
          mode="inline"
          items={menuItems}
          inlineCollapsed={collapsed}
          className="h-full !border-r-0"
          selectedKeys={selectedKey ? [selectedKey] : []}
          openKeys={openKeys}
          onClick={({ key }) => {
            navigate({
              to: key,
            })
          }}
          onOpenChange={setOpenKeys}
        />
      </div>
    </aside>
  )
}
