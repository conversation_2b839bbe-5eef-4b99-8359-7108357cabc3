import numeral from 'numeral'

// 精度配置（保留几位小数）
const PRECISION = 2

/**
 * 将值转换为数字，处理 undefined/null/空字符串
 * @param value 待转换的值
 * @returns 转换后的数字（默认0）
 */
const toNumber = (value: string | number | undefined): number => {
  if (value === undefined || value === null || value === '') return 0
  return numeral(value).value() || 0
}

/**
 * 格式化数字，保留指定精度
 * @param value 待格式化的数字
 * @returns 格式化后的数字
 */
const formatNumber = (value: number, precision = PRECISION): number => {
  // 使用numeral格式化后再转为数字，减少精度误差
  return Number(numeral(value).format(`0.${'0'.repeat(precision)}`))
}

/**
 * 数字相加
 * @param values 多个待相加的值
 * @returns 总和（已格式化）
 */
const addNumbers = (...values: (string | number | undefined)[]): number => {
  const total = values.reduce((sum, val) => Number(sum || 0) + toNumber(val), 0)
  return formatNumber(Number(total))
}

/**
 * 数字相减（第一个值减去后续所有值）
 * @param values 多个待相减的值（第一个为被减数）
 * @returns 差值（已格式化）
 */
const subtractNumbers = (
  ...values: (string | number | undefined)[]
): number => {
  if (values.length === 0) return 0
  // 第一个值为被减数，后续为减数
  const [first, ...rest] = values.map(toNumber)
  const result = rest.reduce((diff, val) => diff - val, first)
  return formatNumber(result)
}

/**
 * 数字相乘
 * @param values 多个待相乘的值
 * @returns 乘积（已格式化）
 */
const multiplyNumbers = (
  ...values: (string | number | undefined)[]
): number => {
  if (values.length === 0) return 0
  // 初始值为1（乘法的单位元）
  const product = values.reduce(
    (prod, val) => Number(prod || 0) * toNumber(val),
    1,
  )
  return formatNumber(Number(product))
}

/**
 * 数字相除（第一个值除以后续所有值）
 * @param values 多个待相除的值（第一个为被除数）
 * @returns 商（已格式化，除数为0时返回0）
 */
const divideNumbers = (...values: (string | number | undefined)[]): number => {
  if (values.length === 0) return 0
  const [first, ...rest] = values.map(toNumber)
  // 处理被除数为0的情况
  if (first === 0) return 0
  // 处理除数为0的情况
  const divisors = rest.filter((val) => val === 0)
  if (divisors.length > 0) return 0
  // 依次相除
  const result = rest.reduce((quotient, val) => quotient / val, first)
  return formatNumber(result)
}

export {
  addNumbers,
  subtractNumbers,
  multiplyNumbers,
  divideNumbers,
  toNumber,
  formatNumber,
}
