import dayjs from 'dayjs'
/**
 * 从季度字符串（如 "2025-Q1"）中提取年份和季度
 * @param quarterStr - 季度字符串，格式为 "YYYY-QN"（N为1-4的数字）
 * @returns 包含年份和季度的对象，格式 { year: number; quarter: number }
 * @throws 当输入格式不符合要求时抛出错误
 */
export function extractYearAndQuarter(quarterStr: string): {
  year: number
  quarter: number
} {
  const regex = /^(\d{4})-Q([1-4])$/
  const match = quarterStr.match(regex)

  if (!match) {
    throw new Error(
      `无效的季度格式: ${quarterStr}，请使用 "YYYY-QN" 格式（如 "2025-Q1"）`,
    )
  }

  const year = parseInt(match[1], 10)
  const quarter = parseInt(match[2], 10)

  return { year, quarter }
}

/**
 * 将日期字符串转换为季度字符串（如 "2025-Q1"）
 * @param dateString - 日期字符串，格式为 "YYYY-MM-DD HH:mm:ss"
 * @returns 季度字符串，格式为 "YYYY-QN"（N为1-4的数字）
 * @throws 当输入格式不符合要求时抛出错误
 */
export function convertToQuarter(dateString: string): {
  year: number
  quarter: number
} {
  const date = dayjs(dateString)
  if (!date.isValid()) {
    throw new Error('无效的日期格式，请请使用类似"2025-06-01 00:00:00"的格式')
  }
  const year = date.year()

  const month = date.month() + 1
  const quarter = Math.ceil(month / 3)

  return { year, quarter }
}
