import { AUTH_BASE_URL, AUTH_CLIENT_ID } from '@/config'

export const getToken = (): string | null => {
  return localStorage.getItem('zttb.token')
}

export const setToken = (token: string): void => {
  localStorage.setItem('zttb.token', token)
}

export const removeToken = (): void => {
  localStorage.removeItem('zttb.token')
}

export const getLoginURL = (
  redirectPath = window.location.pathname + window.location.search,
): string => {
  const { host, protocol } = window.location
  const baseRedirectUri = `${protocol}//${host}/authentication/callback`
  //双重编码确保特殊字符被完整保留
  const encodedRedirectPath = encodeURIComponent(
    encodeURIComponent(redirectPath),
  )

  return `${AUTH_BASE_URL}/sso/polyOAuth/authorize?response_type=code&client_id=${AUTH_CLIENT_ID}&redirect_uri=${encodeURIComponent(baseRedirectUri)}&scope=openid&state=${encodedRedirectPath}`
}

export const getLogoutURL = (): string => {
  return `${AUTH_BASE_URL}/sso/polyOAuth/logout?client_id=${AUTH_CLIENT_ID}&post_logout_redirect_uri=${encodeURIComponent(
    getLoginURL(),
  )}`
}
