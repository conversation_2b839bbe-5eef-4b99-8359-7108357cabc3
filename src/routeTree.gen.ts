/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as AuthRouteImport } from './routes/_auth'
import { Route as AuthIndexRouteImport } from './routes/_auth/index'
import { Route as AuthenticationCallbackRouteImport } from './routes/authentication/callback'
import { Route as AuthStrategicReportRouteRouteImport } from './routes/_auth/strategic-report/route'
import { Route as AuthDataSummaryRouteRouteImport } from './routes/_auth/data-summary/route'
import { Route as AuthBasicReportRouteRouteImport } from './routes/_auth/basic-report/route'
import { Route as AuthStrategicReportDevelopmentIndexRouteRouteImport } from './routes/_auth/strategic-report/development-index/route'
import { Route as AuthDataSummaryIndexRouteImport } from './routes/_auth/data-summary/index'
import { Route as AuthBasicReportIndexRouteImport } from './routes/_auth/basic-report/index'
import { Route as AuthStrategicReportDeviceUpdateRouteRouteImport } from './routes/_auth/strategic-report/device-update/route'
import { Route as AuthDataSummaryMonthlyReportRouteRouteImport } from './routes/_auth/data-summary/monthly-report/route'
import { Route as AuthDataSummaryInvestmentPlanRouteRouteImport } from './routes/_auth/data-summary/investment-plan/route'
import { Route as AuthDataSummaryCompletionStatusRouteRouteImport } from './routes/_auth/data-summary/completion-status/route'
import { Route as AuthBasicReportPostEvaluationRouteRouteImport } from './routes/_auth/basic-report/post-evaluation/route'
import { Route as AuthBasicReportFixedAssetsRouteRouteImport } from './routes/_auth/basic-report/fixed-assets/route'
import { Route as AuthBasicReportEquityProjectsRouteRouteImport } from './routes/_auth/basic-report/equity-projects/route'
import { Route as AuthStrategicReportDeviceUpdateIndexRouteImport } from './routes/_auth/strategic-report/device-update/index'
import { Route as AuthStrategicReportDevelopmentIndexIndexRouteImport } from './routes/_auth/strategic-report/development-index/index'
import { Route as AuthDataSummaryMonthlyReportIndexRouteImport } from './routes/_auth/data-summary/monthly-report/index'
import { Route as AuthDataSummaryInvestmentPlanIndexRouteImport } from './routes/_auth/data-summary/investment-plan/index'
import { Route as AuthDataSummaryCompletionStatusIndexRouteImport } from './routes/_auth/data-summary/completion-status/index'
import { Route as AuthBasicReportPostEvaluationIndexRouteImport } from './routes/_auth/basic-report/post-evaluation/index'
import { Route as AuthBasicReportFixedAssetsIndexRouteImport } from './routes/_auth/basic-report/fixed-assets/index'
import { Route as AuthBasicReportEquityProjectsIndexRouteImport } from './routes/_auth/basic-report/equity-projects/index'
import { Route as AuthStrategicReportDeviceUpdateCreateRouteImport } from './routes/_auth/strategic-report/device-update/create'
import { Route as AuthStrategicReportDevelopmentIndexCreateRouteImport } from './routes/_auth/strategic-report/development-index/create'
import { Route as AuthDataSummaryMonthlyReportCreateRouteImport } from './routes/_auth/data-summary/monthly-report/create'
import { Route as AuthBasicReportPostEvaluationCreateRouteImport } from './routes/_auth/basic-report/post-evaluation/create'
import { Route as AuthBasicReportFixedAssetsCreateRouteImport } from './routes/_auth/basic-report/fixed-assets/create'
import { Route as AuthBasicReportEquityProjectsCreateRouteImport } from './routes/_auth/basic-report/equity-projects/create'
import { Route as AuthDataSummaryInvestmentPlanTableRouteRouteImport } from './routes/_auth/data-summary/investment-plan/table/route'
import { Route as AuthDataSummaryCompletionStatusTableRouteRouteImport } from './routes/_auth/data-summary/completion-status/table/route'
import { Route as AuthDataSummaryInvestmentPlanTableIndexRouteImport } from './routes/_auth/data-summary/investment-plan/table/index'
import { Route as AuthDataSummaryCompletionStatusTableIndexRouteImport } from './routes/_auth/data-summary/completion-status/table/index'
import { Route as AuthStrategicReportDeviceUpdateIdUpdateRouteImport } from './routes/_auth/strategic-report/device-update/$id/update'
import { Route as AuthStrategicReportDevelopmentIndexIdViewRouteImport } from './routes/_auth/strategic-report/development-index/$id/view'
import { Route as AuthStrategicReportDevelopmentIndexIdUpdateRouteImport } from './routes/_auth/strategic-report/development-index/$id/update'
import { Route as AuthDataSummaryMonthlyReportIdViewRouteImport } from './routes/_auth/data-summary/monthly-report/$id/view'
import { Route as AuthDataSummaryMonthlyReportIdUpdateRouteImport } from './routes/_auth/data-summary/monthly-report/$id/update'
import { Route as AuthDataSummaryMonthlyReportIdReportRouteImport } from './routes/_auth/data-summary/monthly-report/$id/report'
import { Route as AuthBasicReportPostEvaluationIdUpdateRouteImport } from './routes/_auth/basic-report/post-evaluation/$id/update'
import { Route as AuthBasicReportFixedAssetsIdUpdateRouteImport } from './routes/_auth/basic-report/fixed-assets/$id/update'
import { Route as AuthBasicReportEquityProjectsIdUpdateRouteImport } from './routes/_auth/basic-report/equity-projects/$id/update'
import { Route as AuthDataSummaryInvestmentPlanTableTableNameRouteRouteImport } from './routes/_auth/data-summary/investment-plan/table/$tableName/route'
import { Route as AuthDataSummaryCompletionStatusTableTableNameRouteRouteImport } from './routes/_auth/data-summary/completion-status/table/$tableName/route'

const AuthRoute = AuthRouteImport.update({
  id: '/_auth',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthIndexRoute = AuthIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthRoute,
} as any)
const AuthenticationCallbackRoute = AuthenticationCallbackRouteImport.update({
  id: '/authentication/callback',
  path: '/authentication/callback',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthStrategicReportRouteRoute =
  AuthStrategicReportRouteRouteImport.update({
    id: '/strategic-report',
    path: '/strategic-report',
    getParentRoute: () => AuthRoute,
  } as any)
const AuthDataSummaryRouteRoute = AuthDataSummaryRouteRouteImport.update({
  id: '/data-summary',
  path: '/data-summary',
  getParentRoute: () => AuthRoute,
} as any)
const AuthBasicReportRouteRoute = AuthBasicReportRouteRouteImport.update({
  id: '/basic-report',
  path: '/basic-report',
  getParentRoute: () => AuthRoute,
} as any)
const AuthStrategicReportDevelopmentIndexRouteRoute =
  AuthStrategicReportDevelopmentIndexRouteRouteImport.update({
    id: '/development-index',
    path: '/development-index',
    getParentRoute: () => AuthStrategicReportRouteRoute,
  } as any)
const AuthDataSummaryIndexRoute = AuthDataSummaryIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthDataSummaryRouteRoute,
} as any)
const AuthBasicReportIndexRoute = AuthBasicReportIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthBasicReportRouteRoute,
} as any)
const AuthStrategicReportDeviceUpdateRouteRoute =
  AuthStrategicReportDeviceUpdateRouteRouteImport.update({
    id: '/device-update',
    path: '/device-update',
    getParentRoute: () => AuthStrategicReportRouteRoute,
  } as any)
const AuthDataSummaryMonthlyReportRouteRoute =
  AuthDataSummaryMonthlyReportRouteRouteImport.update({
    id: '/monthly-report',
    path: '/monthly-report',
    getParentRoute: () => AuthDataSummaryRouteRoute,
  } as any)
const AuthDataSummaryInvestmentPlanRouteRoute =
  AuthDataSummaryInvestmentPlanRouteRouteImport.update({
    id: '/investment-plan',
    path: '/investment-plan',
    getParentRoute: () => AuthDataSummaryRouteRoute,
  } as any)
const AuthDataSummaryCompletionStatusRouteRoute =
  AuthDataSummaryCompletionStatusRouteRouteImport.update({
    id: '/completion-status',
    path: '/completion-status',
    getParentRoute: () => AuthDataSummaryRouteRoute,
  } as any)
const AuthBasicReportPostEvaluationRouteRoute =
  AuthBasicReportPostEvaluationRouteRouteImport.update({
    id: '/post-evaluation',
    path: '/post-evaluation',
    getParentRoute: () => AuthBasicReportRouteRoute,
  } as any)
const AuthBasicReportFixedAssetsRouteRoute =
  AuthBasicReportFixedAssetsRouteRouteImport.update({
    id: '/fixed-assets',
    path: '/fixed-assets',
    getParentRoute: () => AuthBasicReportRouteRoute,
  } as any)
const AuthBasicReportEquityProjectsRouteRoute =
  AuthBasicReportEquityProjectsRouteRouteImport.update({
    id: '/equity-projects',
    path: '/equity-projects',
    getParentRoute: () => AuthBasicReportRouteRoute,
  } as any)
const AuthStrategicReportDeviceUpdateIndexRoute =
  AuthStrategicReportDeviceUpdateIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthStrategicReportDeviceUpdateRouteRoute,
  } as any)
const AuthStrategicReportDevelopmentIndexIndexRoute =
  AuthStrategicReportDevelopmentIndexIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthStrategicReportDevelopmentIndexRouteRoute,
  } as any)
const AuthDataSummaryMonthlyReportIndexRoute =
  AuthDataSummaryMonthlyReportIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthDataSummaryMonthlyReportRouteRoute,
  } as any)
const AuthDataSummaryInvestmentPlanIndexRoute =
  AuthDataSummaryInvestmentPlanIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthDataSummaryInvestmentPlanRouteRoute,
  } as any)
const AuthDataSummaryCompletionStatusIndexRoute =
  AuthDataSummaryCompletionStatusIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthDataSummaryCompletionStatusRouteRoute,
  } as any)
const AuthBasicReportPostEvaluationIndexRoute =
  AuthBasicReportPostEvaluationIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthBasicReportPostEvaluationRouteRoute,
  } as any)
const AuthBasicReportFixedAssetsIndexRoute =
  AuthBasicReportFixedAssetsIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthBasicReportFixedAssetsRouteRoute,
  } as any)
const AuthBasicReportEquityProjectsIndexRoute =
  AuthBasicReportEquityProjectsIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthBasicReportEquityProjectsRouteRoute,
  } as any)
const AuthStrategicReportDeviceUpdateCreateRoute =
  AuthStrategicReportDeviceUpdateCreateRouteImport.update({
    id: '/create',
    path: '/create',
    getParentRoute: () => AuthStrategicReportDeviceUpdateRouteRoute,
  } as any)
const AuthStrategicReportDevelopmentIndexCreateRoute =
  AuthStrategicReportDevelopmentIndexCreateRouteImport.update({
    id: '/create',
    path: '/create',
    getParentRoute: () => AuthStrategicReportDevelopmentIndexRouteRoute,
  } as any)
const AuthDataSummaryMonthlyReportCreateRoute =
  AuthDataSummaryMonthlyReportCreateRouteImport.update({
    id: '/create',
    path: '/create',
    getParentRoute: () => AuthDataSummaryMonthlyReportRouteRoute,
  } as any)
const AuthBasicReportPostEvaluationCreateRoute =
  AuthBasicReportPostEvaluationCreateRouteImport.update({
    id: '/create',
    path: '/create',
    getParentRoute: () => AuthBasicReportPostEvaluationRouteRoute,
  } as any)
const AuthBasicReportFixedAssetsCreateRoute =
  AuthBasicReportFixedAssetsCreateRouteImport.update({
    id: '/create',
    path: '/create',
    getParentRoute: () => AuthBasicReportFixedAssetsRouteRoute,
  } as any)
const AuthBasicReportEquityProjectsCreateRoute =
  AuthBasicReportEquityProjectsCreateRouteImport.update({
    id: '/create',
    path: '/create',
    getParentRoute: () => AuthBasicReportEquityProjectsRouteRoute,
  } as any)
const AuthDataSummaryInvestmentPlanTableRouteRoute =
  AuthDataSummaryInvestmentPlanTableRouteRouteImport.update({
    id: '/table',
    path: '/table',
    getParentRoute: () => AuthDataSummaryInvestmentPlanRouteRoute,
  } as any)
const AuthDataSummaryCompletionStatusTableRouteRoute =
  AuthDataSummaryCompletionStatusTableRouteRouteImport.update({
    id: '/table',
    path: '/table',
    getParentRoute: () => AuthDataSummaryCompletionStatusRouteRoute,
  } as any)
const AuthDataSummaryInvestmentPlanTableIndexRoute =
  AuthDataSummaryInvestmentPlanTableIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthDataSummaryInvestmentPlanTableRouteRoute,
  } as any)
const AuthDataSummaryCompletionStatusTableIndexRoute =
  AuthDataSummaryCompletionStatusTableIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthDataSummaryCompletionStatusTableRouteRoute,
  } as any)
const AuthStrategicReportDeviceUpdateIdUpdateRoute =
  AuthStrategicReportDeviceUpdateIdUpdateRouteImport.update({
    id: '/$id/update',
    path: '/$id/update',
    getParentRoute: () => AuthStrategicReportDeviceUpdateRouteRoute,
  } as any)
const AuthStrategicReportDevelopmentIndexIdViewRoute =
  AuthStrategicReportDevelopmentIndexIdViewRouteImport.update({
    id: '/$id/view',
    path: '/$id/view',
    getParentRoute: () => AuthStrategicReportDevelopmentIndexRouteRoute,
  } as any)
const AuthStrategicReportDevelopmentIndexIdUpdateRoute =
  AuthStrategicReportDevelopmentIndexIdUpdateRouteImport.update({
    id: '/$id/update',
    path: '/$id/update',
    getParentRoute: () => AuthStrategicReportDevelopmentIndexRouteRoute,
  } as any)
const AuthDataSummaryMonthlyReportIdViewRoute =
  AuthDataSummaryMonthlyReportIdViewRouteImport.update({
    id: '/$id/view',
    path: '/$id/view',
    getParentRoute: () => AuthDataSummaryMonthlyReportRouteRoute,
  } as any)
const AuthDataSummaryMonthlyReportIdUpdateRoute =
  AuthDataSummaryMonthlyReportIdUpdateRouteImport.update({
    id: '/$id/update',
    path: '/$id/update',
    getParentRoute: () => AuthDataSummaryMonthlyReportRouteRoute,
  } as any)
const AuthDataSummaryMonthlyReportIdReportRoute =
  AuthDataSummaryMonthlyReportIdReportRouteImport.update({
    id: '/$id/report',
    path: '/$id/report',
    getParentRoute: () => AuthDataSummaryMonthlyReportRouteRoute,
  } as any)
const AuthBasicReportPostEvaluationIdUpdateRoute =
  AuthBasicReportPostEvaluationIdUpdateRouteImport.update({
    id: '/$id/update',
    path: '/$id/update',
    getParentRoute: () => AuthBasicReportPostEvaluationRouteRoute,
  } as any)
const AuthBasicReportFixedAssetsIdUpdateRoute =
  AuthBasicReportFixedAssetsIdUpdateRouteImport.update({
    id: '/$id/update',
    path: '/$id/update',
    getParentRoute: () => AuthBasicReportFixedAssetsRouteRoute,
  } as any)
const AuthBasicReportEquityProjectsIdUpdateRoute =
  AuthBasicReportEquityProjectsIdUpdateRouteImport.update({
    id: '/$id/update',
    path: '/$id/update',
    getParentRoute: () => AuthBasicReportEquityProjectsRouteRoute,
  } as any)
const AuthDataSummaryInvestmentPlanTableTableNameRouteRoute =
  AuthDataSummaryInvestmentPlanTableTableNameRouteRouteImport.update({
    id: '/$tableName',
    path: '/$tableName',
    getParentRoute: () => AuthDataSummaryInvestmentPlanTableRouteRoute,
  } as any)
const AuthDataSummaryCompletionStatusTableTableNameRouteRoute =
  AuthDataSummaryCompletionStatusTableTableNameRouteRouteImport.update({
    id: '/$tableName',
    path: '/$tableName',
    getParentRoute: () => AuthDataSummaryCompletionStatusTableRouteRoute,
  } as any)

export interface FileRoutesByFullPath {
  '/basic-report': typeof AuthBasicReportRouteRouteWithChildren
  '/data-summary': typeof AuthDataSummaryRouteRouteWithChildren
  '/strategic-report': typeof AuthStrategicReportRouteRouteWithChildren
  '/authentication/callback': typeof AuthenticationCallbackRoute
  '/': typeof AuthIndexRoute
  '/basic-report/equity-projects': typeof AuthBasicReportEquityProjectsRouteRouteWithChildren
  '/basic-report/fixed-assets': typeof AuthBasicReportFixedAssetsRouteRouteWithChildren
  '/basic-report/post-evaluation': typeof AuthBasicReportPostEvaluationRouteRouteWithChildren
  '/data-summary/completion-status': typeof AuthDataSummaryCompletionStatusRouteRouteWithChildren
  '/data-summary/investment-plan': typeof AuthDataSummaryInvestmentPlanRouteRouteWithChildren
  '/data-summary/monthly-report': typeof AuthDataSummaryMonthlyReportRouteRouteWithChildren
  '/strategic-report/development-index': typeof AuthStrategicReportDevelopmentIndexRouteRouteWithChildren
  '/strategic-report/device-update': typeof AuthStrategicReportDeviceUpdateRouteRouteWithChildren
  '/basic-report/': typeof AuthBasicReportIndexRoute
  '/data-summary/': typeof AuthDataSummaryIndexRoute
  '/data-summary/completion-status/table': typeof AuthDataSummaryCompletionStatusTableRouteRouteWithChildren
  '/data-summary/investment-plan/table': typeof AuthDataSummaryInvestmentPlanTableRouteRouteWithChildren
  '/basic-report/equity-projects/create': typeof AuthBasicReportEquityProjectsCreateRoute
  '/basic-report/fixed-assets/create': typeof AuthBasicReportFixedAssetsCreateRoute
  '/basic-report/post-evaluation/create': typeof AuthBasicReportPostEvaluationCreateRoute
  '/data-summary/monthly-report/create': typeof AuthDataSummaryMonthlyReportCreateRoute
  '/strategic-report/development-index/create': typeof AuthStrategicReportDevelopmentIndexCreateRoute
  '/strategic-report/device-update/create': typeof AuthStrategicReportDeviceUpdateCreateRoute
  '/basic-report/equity-projects/': typeof AuthBasicReportEquityProjectsIndexRoute
  '/basic-report/fixed-assets/': typeof AuthBasicReportFixedAssetsIndexRoute
  '/basic-report/post-evaluation/': typeof AuthBasicReportPostEvaluationIndexRoute
  '/data-summary/completion-status/': typeof AuthDataSummaryCompletionStatusIndexRoute
  '/data-summary/investment-plan/': typeof AuthDataSummaryInvestmentPlanIndexRoute
  '/data-summary/monthly-report/': typeof AuthDataSummaryMonthlyReportIndexRoute
  '/strategic-report/development-index/': typeof AuthStrategicReportDevelopmentIndexIndexRoute
  '/strategic-report/device-update/': typeof AuthStrategicReportDeviceUpdateIndexRoute
  '/data-summary/completion-status/table/$tableName': typeof AuthDataSummaryCompletionStatusTableTableNameRouteRoute
  '/data-summary/investment-plan/table/$tableName': typeof AuthDataSummaryInvestmentPlanTableTableNameRouteRoute
  '/basic-report/equity-projects/$id/update': typeof AuthBasicReportEquityProjectsIdUpdateRoute
  '/basic-report/fixed-assets/$id/update': typeof AuthBasicReportFixedAssetsIdUpdateRoute
  '/basic-report/post-evaluation/$id/update': typeof AuthBasicReportPostEvaluationIdUpdateRoute
  '/data-summary/monthly-report/$id/report': typeof AuthDataSummaryMonthlyReportIdReportRoute
  '/data-summary/monthly-report/$id/update': typeof AuthDataSummaryMonthlyReportIdUpdateRoute
  '/data-summary/monthly-report/$id/view': typeof AuthDataSummaryMonthlyReportIdViewRoute
  '/strategic-report/development-index/$id/update': typeof AuthStrategicReportDevelopmentIndexIdUpdateRoute
  '/strategic-report/development-index/$id/view': typeof AuthStrategicReportDevelopmentIndexIdViewRoute
  '/strategic-report/device-update/$id/update': typeof AuthStrategicReportDeviceUpdateIdUpdateRoute
  '/data-summary/completion-status/table/': typeof AuthDataSummaryCompletionStatusTableIndexRoute
  '/data-summary/investment-plan/table/': typeof AuthDataSummaryInvestmentPlanTableIndexRoute
}
export interface FileRoutesByTo {
  '/strategic-report': typeof AuthStrategicReportRouteRouteWithChildren
  '/authentication/callback': typeof AuthenticationCallbackRoute
  '/': typeof AuthIndexRoute
  '/basic-report': typeof AuthBasicReportIndexRoute
  '/data-summary': typeof AuthDataSummaryIndexRoute
  '/basic-report/equity-projects/create': typeof AuthBasicReportEquityProjectsCreateRoute
  '/basic-report/fixed-assets/create': typeof AuthBasicReportFixedAssetsCreateRoute
  '/basic-report/post-evaluation/create': typeof AuthBasicReportPostEvaluationCreateRoute
  '/data-summary/monthly-report/create': typeof AuthDataSummaryMonthlyReportCreateRoute
  '/strategic-report/development-index/create': typeof AuthStrategicReportDevelopmentIndexCreateRoute
  '/strategic-report/device-update/create': typeof AuthStrategicReportDeviceUpdateCreateRoute
  '/basic-report/equity-projects': typeof AuthBasicReportEquityProjectsIndexRoute
  '/basic-report/fixed-assets': typeof AuthBasicReportFixedAssetsIndexRoute
  '/basic-report/post-evaluation': typeof AuthBasicReportPostEvaluationIndexRoute
  '/data-summary/completion-status': typeof AuthDataSummaryCompletionStatusIndexRoute
  '/data-summary/investment-plan': typeof AuthDataSummaryInvestmentPlanIndexRoute
  '/data-summary/monthly-report': typeof AuthDataSummaryMonthlyReportIndexRoute
  '/strategic-report/development-index': typeof AuthStrategicReportDevelopmentIndexIndexRoute
  '/strategic-report/device-update': typeof AuthStrategicReportDeviceUpdateIndexRoute
  '/data-summary/completion-status/table/$tableName': typeof AuthDataSummaryCompletionStatusTableTableNameRouteRoute
  '/data-summary/investment-plan/table/$tableName': typeof AuthDataSummaryInvestmentPlanTableTableNameRouteRoute
  '/basic-report/equity-projects/$id/update': typeof AuthBasicReportEquityProjectsIdUpdateRoute
  '/basic-report/fixed-assets/$id/update': typeof AuthBasicReportFixedAssetsIdUpdateRoute
  '/basic-report/post-evaluation/$id/update': typeof AuthBasicReportPostEvaluationIdUpdateRoute
  '/data-summary/monthly-report/$id/report': typeof AuthDataSummaryMonthlyReportIdReportRoute
  '/data-summary/monthly-report/$id/update': typeof AuthDataSummaryMonthlyReportIdUpdateRoute
  '/data-summary/monthly-report/$id/view': typeof AuthDataSummaryMonthlyReportIdViewRoute
  '/strategic-report/development-index/$id/update': typeof AuthStrategicReportDevelopmentIndexIdUpdateRoute
  '/strategic-report/development-index/$id/view': typeof AuthStrategicReportDevelopmentIndexIdViewRoute
  '/strategic-report/device-update/$id/update': typeof AuthStrategicReportDeviceUpdateIdUpdateRoute
  '/data-summary/completion-status/table': typeof AuthDataSummaryCompletionStatusTableIndexRoute
  '/data-summary/investment-plan/table': typeof AuthDataSummaryInvestmentPlanTableIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/_auth': typeof AuthRouteWithChildren
  '/_auth/basic-report': typeof AuthBasicReportRouteRouteWithChildren
  '/_auth/data-summary': typeof AuthDataSummaryRouteRouteWithChildren
  '/_auth/strategic-report': typeof AuthStrategicReportRouteRouteWithChildren
  '/authentication/callback': typeof AuthenticationCallbackRoute
  '/_auth/': typeof AuthIndexRoute
  '/_auth/basic-report/equity-projects': typeof AuthBasicReportEquityProjectsRouteRouteWithChildren
  '/_auth/basic-report/fixed-assets': typeof AuthBasicReportFixedAssetsRouteRouteWithChildren
  '/_auth/basic-report/post-evaluation': typeof AuthBasicReportPostEvaluationRouteRouteWithChildren
  '/_auth/data-summary/completion-status': typeof AuthDataSummaryCompletionStatusRouteRouteWithChildren
  '/_auth/data-summary/investment-plan': typeof AuthDataSummaryInvestmentPlanRouteRouteWithChildren
  '/_auth/data-summary/monthly-report': typeof AuthDataSummaryMonthlyReportRouteRouteWithChildren
  '/_auth/strategic-report/development-index': typeof AuthStrategicReportDevelopmentIndexRouteRouteWithChildren
  '/_auth/strategic-report/device-update': typeof AuthStrategicReportDeviceUpdateRouteRouteWithChildren
  '/_auth/basic-report/': typeof AuthBasicReportIndexRoute
  '/_auth/data-summary/': typeof AuthDataSummaryIndexRoute
  '/_auth/data-summary/completion-status/table': typeof AuthDataSummaryCompletionStatusTableRouteRouteWithChildren
  '/_auth/data-summary/investment-plan/table': typeof AuthDataSummaryInvestmentPlanTableRouteRouteWithChildren
  '/_auth/basic-report/equity-projects/create': typeof AuthBasicReportEquityProjectsCreateRoute
  '/_auth/basic-report/fixed-assets/create': typeof AuthBasicReportFixedAssetsCreateRoute
  '/_auth/basic-report/post-evaluation/create': typeof AuthBasicReportPostEvaluationCreateRoute
  '/_auth/data-summary/monthly-report/create': typeof AuthDataSummaryMonthlyReportCreateRoute
  '/_auth/strategic-report/development-index/create': typeof AuthStrategicReportDevelopmentIndexCreateRoute
  '/_auth/strategic-report/device-update/create': typeof AuthStrategicReportDeviceUpdateCreateRoute
  '/_auth/basic-report/equity-projects/': typeof AuthBasicReportEquityProjectsIndexRoute
  '/_auth/basic-report/fixed-assets/': typeof AuthBasicReportFixedAssetsIndexRoute
  '/_auth/basic-report/post-evaluation/': typeof AuthBasicReportPostEvaluationIndexRoute
  '/_auth/data-summary/completion-status/': typeof AuthDataSummaryCompletionStatusIndexRoute
  '/_auth/data-summary/investment-plan/': typeof AuthDataSummaryInvestmentPlanIndexRoute
  '/_auth/data-summary/monthly-report/': typeof AuthDataSummaryMonthlyReportIndexRoute
  '/_auth/strategic-report/development-index/': typeof AuthStrategicReportDevelopmentIndexIndexRoute
  '/_auth/strategic-report/device-update/': typeof AuthStrategicReportDeviceUpdateIndexRoute
  '/_auth/data-summary/completion-status/table/$tableName': typeof AuthDataSummaryCompletionStatusTableTableNameRouteRoute
  '/_auth/data-summary/investment-plan/table/$tableName': typeof AuthDataSummaryInvestmentPlanTableTableNameRouteRoute
  '/_auth/basic-report/equity-projects/$id/update': typeof AuthBasicReportEquityProjectsIdUpdateRoute
  '/_auth/basic-report/fixed-assets/$id/update': typeof AuthBasicReportFixedAssetsIdUpdateRoute
  '/_auth/basic-report/post-evaluation/$id/update': typeof AuthBasicReportPostEvaluationIdUpdateRoute
  '/_auth/data-summary/monthly-report/$id/report': typeof AuthDataSummaryMonthlyReportIdReportRoute
  '/_auth/data-summary/monthly-report/$id/update': typeof AuthDataSummaryMonthlyReportIdUpdateRoute
  '/_auth/data-summary/monthly-report/$id/view': typeof AuthDataSummaryMonthlyReportIdViewRoute
  '/_auth/strategic-report/development-index/$id/update': typeof AuthStrategicReportDevelopmentIndexIdUpdateRoute
  '/_auth/strategic-report/development-index/$id/view': typeof AuthStrategicReportDevelopmentIndexIdViewRoute
  '/_auth/strategic-report/device-update/$id/update': typeof AuthStrategicReportDeviceUpdateIdUpdateRoute
  '/_auth/data-summary/completion-status/table/': typeof AuthDataSummaryCompletionStatusTableIndexRoute
  '/_auth/data-summary/investment-plan/table/': typeof AuthDataSummaryInvestmentPlanTableIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/basic-report'
    | '/data-summary'
    | '/strategic-report'
    | '/authentication/callback'
    | '/'
    | '/basic-report/equity-projects'
    | '/basic-report/fixed-assets'
    | '/basic-report/post-evaluation'
    | '/data-summary/completion-status'
    | '/data-summary/investment-plan'
    | '/data-summary/monthly-report'
    | '/strategic-report/development-index'
    | '/strategic-report/device-update'
    | '/basic-report/'
    | '/data-summary/'
    | '/data-summary/completion-status/table'
    | '/data-summary/investment-plan/table'
    | '/basic-report/equity-projects/create'
    | '/basic-report/fixed-assets/create'
    | '/basic-report/post-evaluation/create'
    | '/data-summary/monthly-report/create'
    | '/strategic-report/development-index/create'
    | '/strategic-report/device-update/create'
    | '/basic-report/equity-projects/'
    | '/basic-report/fixed-assets/'
    | '/basic-report/post-evaluation/'
    | '/data-summary/completion-status/'
    | '/data-summary/investment-plan/'
    | '/data-summary/monthly-report/'
    | '/strategic-report/development-index/'
    | '/strategic-report/device-update/'
    | '/data-summary/completion-status/table/$tableName'
    | '/data-summary/investment-plan/table/$tableName'
    | '/basic-report/equity-projects/$id/update'
    | '/basic-report/fixed-assets/$id/update'
    | '/basic-report/post-evaluation/$id/update'
    | '/data-summary/monthly-report/$id/report'
    | '/data-summary/monthly-report/$id/update'
    | '/data-summary/monthly-report/$id/view'
    | '/strategic-report/development-index/$id/update'
    | '/strategic-report/development-index/$id/view'
    | '/strategic-report/device-update/$id/update'
    | '/data-summary/completion-status/table/'
    | '/data-summary/investment-plan/table/'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/strategic-report'
    | '/authentication/callback'
    | '/'
    | '/basic-report'
    | '/data-summary'
    | '/basic-report/equity-projects/create'
    | '/basic-report/fixed-assets/create'
    | '/basic-report/post-evaluation/create'
    | '/data-summary/monthly-report/create'
    | '/strategic-report/development-index/create'
    | '/strategic-report/device-update/create'
    | '/basic-report/equity-projects'
    | '/basic-report/fixed-assets'
    | '/basic-report/post-evaluation'
    | '/data-summary/completion-status'
    | '/data-summary/investment-plan'
    | '/data-summary/monthly-report'
    | '/strategic-report/development-index'
    | '/strategic-report/device-update'
    | '/data-summary/completion-status/table/$tableName'
    | '/data-summary/investment-plan/table/$tableName'
    | '/basic-report/equity-projects/$id/update'
    | '/basic-report/fixed-assets/$id/update'
    | '/basic-report/post-evaluation/$id/update'
    | '/data-summary/monthly-report/$id/report'
    | '/data-summary/monthly-report/$id/update'
    | '/data-summary/monthly-report/$id/view'
    | '/strategic-report/development-index/$id/update'
    | '/strategic-report/development-index/$id/view'
    | '/strategic-report/device-update/$id/update'
    | '/data-summary/completion-status/table'
    | '/data-summary/investment-plan/table'
  id:
    | '__root__'
    | '/_auth'
    | '/_auth/basic-report'
    | '/_auth/data-summary'
    | '/_auth/strategic-report'
    | '/authentication/callback'
    | '/_auth/'
    | '/_auth/basic-report/equity-projects'
    | '/_auth/basic-report/fixed-assets'
    | '/_auth/basic-report/post-evaluation'
    | '/_auth/data-summary/completion-status'
    | '/_auth/data-summary/investment-plan'
    | '/_auth/data-summary/monthly-report'
    | '/_auth/strategic-report/development-index'
    | '/_auth/strategic-report/device-update'
    | '/_auth/basic-report/'
    | '/_auth/data-summary/'
    | '/_auth/data-summary/completion-status/table'
    | '/_auth/data-summary/investment-plan/table'
    | '/_auth/basic-report/equity-projects/create'
    | '/_auth/basic-report/fixed-assets/create'
    | '/_auth/basic-report/post-evaluation/create'
    | '/_auth/data-summary/monthly-report/create'
    | '/_auth/strategic-report/development-index/create'
    | '/_auth/strategic-report/device-update/create'
    | '/_auth/basic-report/equity-projects/'
    | '/_auth/basic-report/fixed-assets/'
    | '/_auth/basic-report/post-evaluation/'
    | '/_auth/data-summary/completion-status/'
    | '/_auth/data-summary/investment-plan/'
    | '/_auth/data-summary/monthly-report/'
    | '/_auth/strategic-report/development-index/'
    | '/_auth/strategic-report/device-update/'
    | '/_auth/data-summary/completion-status/table/$tableName'
    | '/_auth/data-summary/investment-plan/table/$tableName'
    | '/_auth/basic-report/equity-projects/$id/update'
    | '/_auth/basic-report/fixed-assets/$id/update'
    | '/_auth/basic-report/post-evaluation/$id/update'
    | '/_auth/data-summary/monthly-report/$id/report'
    | '/_auth/data-summary/monthly-report/$id/update'
    | '/_auth/data-summary/monthly-report/$id/view'
    | '/_auth/strategic-report/development-index/$id/update'
    | '/_auth/strategic-report/development-index/$id/view'
    | '/_auth/strategic-report/device-update/$id/update'
    | '/_auth/data-summary/completion-status/table/'
    | '/_auth/data-summary/investment-plan/table/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  AuthRoute: typeof AuthRouteWithChildren
  AuthenticationCallbackRoute: typeof AuthenticationCallbackRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_auth': {
      id: '/_auth'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_auth/': {
      id: '/_auth/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof AuthIndexRouteImport
      parentRoute: typeof AuthRoute
    }
    '/authentication/callback': {
      id: '/authentication/callback'
      path: '/authentication/callback'
      fullPath: '/authentication/callback'
      preLoaderRoute: typeof AuthenticationCallbackRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_auth/strategic-report': {
      id: '/_auth/strategic-report'
      path: '/strategic-report'
      fullPath: '/strategic-report'
      preLoaderRoute: typeof AuthStrategicReportRouteRouteImport
      parentRoute: typeof AuthRoute
    }
    '/_auth/data-summary': {
      id: '/_auth/data-summary'
      path: '/data-summary'
      fullPath: '/data-summary'
      preLoaderRoute: typeof AuthDataSummaryRouteRouteImport
      parentRoute: typeof AuthRoute
    }
    '/_auth/basic-report': {
      id: '/_auth/basic-report'
      path: '/basic-report'
      fullPath: '/basic-report'
      preLoaderRoute: typeof AuthBasicReportRouteRouteImport
      parentRoute: typeof AuthRoute
    }
    '/_auth/strategic-report/development-index': {
      id: '/_auth/strategic-report/development-index'
      path: '/development-index'
      fullPath: '/strategic-report/development-index'
      preLoaderRoute: typeof AuthStrategicReportDevelopmentIndexRouteRouteImport
      parentRoute: typeof AuthStrategicReportRouteRoute
    }
    '/_auth/data-summary/': {
      id: '/_auth/data-summary/'
      path: '/'
      fullPath: '/data-summary/'
      preLoaderRoute: typeof AuthDataSummaryIndexRouteImport
      parentRoute: typeof AuthDataSummaryRouteRoute
    }
    '/_auth/basic-report/': {
      id: '/_auth/basic-report/'
      path: '/'
      fullPath: '/basic-report/'
      preLoaderRoute: typeof AuthBasicReportIndexRouteImport
      parentRoute: typeof AuthBasicReportRouteRoute
    }
    '/_auth/strategic-report/device-update': {
      id: '/_auth/strategic-report/device-update'
      path: '/device-update'
      fullPath: '/strategic-report/device-update'
      preLoaderRoute: typeof AuthStrategicReportDeviceUpdateRouteRouteImport
      parentRoute: typeof AuthStrategicReportRouteRoute
    }
    '/_auth/data-summary/monthly-report': {
      id: '/_auth/data-summary/monthly-report'
      path: '/monthly-report'
      fullPath: '/data-summary/monthly-report'
      preLoaderRoute: typeof AuthDataSummaryMonthlyReportRouteRouteImport
      parentRoute: typeof AuthDataSummaryRouteRoute
    }
    '/_auth/data-summary/investment-plan': {
      id: '/_auth/data-summary/investment-plan'
      path: '/investment-plan'
      fullPath: '/data-summary/investment-plan'
      preLoaderRoute: typeof AuthDataSummaryInvestmentPlanRouteRouteImport
      parentRoute: typeof AuthDataSummaryRouteRoute
    }
    '/_auth/data-summary/completion-status': {
      id: '/_auth/data-summary/completion-status'
      path: '/completion-status'
      fullPath: '/data-summary/completion-status'
      preLoaderRoute: typeof AuthDataSummaryCompletionStatusRouteRouteImport
      parentRoute: typeof AuthDataSummaryRouteRoute
    }
    '/_auth/basic-report/post-evaluation': {
      id: '/_auth/basic-report/post-evaluation'
      path: '/post-evaluation'
      fullPath: '/basic-report/post-evaluation'
      preLoaderRoute: typeof AuthBasicReportPostEvaluationRouteRouteImport
      parentRoute: typeof AuthBasicReportRouteRoute
    }
    '/_auth/basic-report/fixed-assets': {
      id: '/_auth/basic-report/fixed-assets'
      path: '/fixed-assets'
      fullPath: '/basic-report/fixed-assets'
      preLoaderRoute: typeof AuthBasicReportFixedAssetsRouteRouteImport
      parentRoute: typeof AuthBasicReportRouteRoute
    }
    '/_auth/basic-report/equity-projects': {
      id: '/_auth/basic-report/equity-projects'
      path: '/equity-projects'
      fullPath: '/basic-report/equity-projects'
      preLoaderRoute: typeof AuthBasicReportEquityProjectsRouteRouteImport
      parentRoute: typeof AuthBasicReportRouteRoute
    }
    '/_auth/strategic-report/device-update/': {
      id: '/_auth/strategic-report/device-update/'
      path: '/'
      fullPath: '/strategic-report/device-update/'
      preLoaderRoute: typeof AuthStrategicReportDeviceUpdateIndexRouteImport
      parentRoute: typeof AuthStrategicReportDeviceUpdateRouteRoute
    }
    '/_auth/strategic-report/development-index/': {
      id: '/_auth/strategic-report/development-index/'
      path: '/'
      fullPath: '/strategic-report/development-index/'
      preLoaderRoute: typeof AuthStrategicReportDevelopmentIndexIndexRouteImport
      parentRoute: typeof AuthStrategicReportDevelopmentIndexRouteRoute
    }
    '/_auth/data-summary/monthly-report/': {
      id: '/_auth/data-summary/monthly-report/'
      path: '/'
      fullPath: '/data-summary/monthly-report/'
      preLoaderRoute: typeof AuthDataSummaryMonthlyReportIndexRouteImport
      parentRoute: typeof AuthDataSummaryMonthlyReportRouteRoute
    }
    '/_auth/data-summary/investment-plan/': {
      id: '/_auth/data-summary/investment-plan/'
      path: '/'
      fullPath: '/data-summary/investment-plan/'
      preLoaderRoute: typeof AuthDataSummaryInvestmentPlanIndexRouteImport
      parentRoute: typeof AuthDataSummaryInvestmentPlanRouteRoute
    }
    '/_auth/data-summary/completion-status/': {
      id: '/_auth/data-summary/completion-status/'
      path: '/'
      fullPath: '/data-summary/completion-status/'
      preLoaderRoute: typeof AuthDataSummaryCompletionStatusIndexRouteImport
      parentRoute: typeof AuthDataSummaryCompletionStatusRouteRoute
    }
    '/_auth/basic-report/post-evaluation/': {
      id: '/_auth/basic-report/post-evaluation/'
      path: '/'
      fullPath: '/basic-report/post-evaluation/'
      preLoaderRoute: typeof AuthBasicReportPostEvaluationIndexRouteImport
      parentRoute: typeof AuthBasicReportPostEvaluationRouteRoute
    }
    '/_auth/basic-report/fixed-assets/': {
      id: '/_auth/basic-report/fixed-assets/'
      path: '/'
      fullPath: '/basic-report/fixed-assets/'
      preLoaderRoute: typeof AuthBasicReportFixedAssetsIndexRouteImport
      parentRoute: typeof AuthBasicReportFixedAssetsRouteRoute
    }
    '/_auth/basic-report/equity-projects/': {
      id: '/_auth/basic-report/equity-projects/'
      path: '/'
      fullPath: '/basic-report/equity-projects/'
      preLoaderRoute: typeof AuthBasicReportEquityProjectsIndexRouteImport
      parentRoute: typeof AuthBasicReportEquityProjectsRouteRoute
    }
    '/_auth/strategic-report/device-update/create': {
      id: '/_auth/strategic-report/device-update/create'
      path: '/create'
      fullPath: '/strategic-report/device-update/create'
      preLoaderRoute: typeof AuthStrategicReportDeviceUpdateCreateRouteImport
      parentRoute: typeof AuthStrategicReportDeviceUpdateRouteRoute
    }
    '/_auth/strategic-report/development-index/create': {
      id: '/_auth/strategic-report/development-index/create'
      path: '/create'
      fullPath: '/strategic-report/development-index/create'
      preLoaderRoute: typeof AuthStrategicReportDevelopmentIndexCreateRouteImport
      parentRoute: typeof AuthStrategicReportDevelopmentIndexRouteRoute
    }
    '/_auth/data-summary/monthly-report/create': {
      id: '/_auth/data-summary/monthly-report/create'
      path: '/create'
      fullPath: '/data-summary/monthly-report/create'
      preLoaderRoute: typeof AuthDataSummaryMonthlyReportCreateRouteImport
      parentRoute: typeof AuthDataSummaryMonthlyReportRouteRoute
    }
    '/_auth/basic-report/post-evaluation/create': {
      id: '/_auth/basic-report/post-evaluation/create'
      path: '/create'
      fullPath: '/basic-report/post-evaluation/create'
      preLoaderRoute: typeof AuthBasicReportPostEvaluationCreateRouteImport
      parentRoute: typeof AuthBasicReportPostEvaluationRouteRoute
    }
    '/_auth/basic-report/fixed-assets/create': {
      id: '/_auth/basic-report/fixed-assets/create'
      path: '/create'
      fullPath: '/basic-report/fixed-assets/create'
      preLoaderRoute: typeof AuthBasicReportFixedAssetsCreateRouteImport
      parentRoute: typeof AuthBasicReportFixedAssetsRouteRoute
    }
    '/_auth/basic-report/equity-projects/create': {
      id: '/_auth/basic-report/equity-projects/create'
      path: '/create'
      fullPath: '/basic-report/equity-projects/create'
      preLoaderRoute: typeof AuthBasicReportEquityProjectsCreateRouteImport
      parentRoute: typeof AuthBasicReportEquityProjectsRouteRoute
    }
    '/_auth/data-summary/investment-plan/table': {
      id: '/_auth/data-summary/investment-plan/table'
      path: '/table'
      fullPath: '/data-summary/investment-plan/table'
      preLoaderRoute: typeof AuthDataSummaryInvestmentPlanTableRouteRouteImport
      parentRoute: typeof AuthDataSummaryInvestmentPlanRouteRoute
    }
    '/_auth/data-summary/completion-status/table': {
      id: '/_auth/data-summary/completion-status/table'
      path: '/table'
      fullPath: '/data-summary/completion-status/table'
      preLoaderRoute: typeof AuthDataSummaryCompletionStatusTableRouteRouteImport
      parentRoute: typeof AuthDataSummaryCompletionStatusRouteRoute
    }
    '/_auth/data-summary/investment-plan/table/': {
      id: '/_auth/data-summary/investment-plan/table/'
      path: '/'
      fullPath: '/data-summary/investment-plan/table/'
      preLoaderRoute: typeof AuthDataSummaryInvestmentPlanTableIndexRouteImport
      parentRoute: typeof AuthDataSummaryInvestmentPlanTableRouteRoute
    }
    '/_auth/data-summary/completion-status/table/': {
      id: '/_auth/data-summary/completion-status/table/'
      path: '/'
      fullPath: '/data-summary/completion-status/table/'
      preLoaderRoute: typeof AuthDataSummaryCompletionStatusTableIndexRouteImport
      parentRoute: typeof AuthDataSummaryCompletionStatusTableRouteRoute
    }
    '/_auth/strategic-report/device-update/$id/update': {
      id: '/_auth/strategic-report/device-update/$id/update'
      path: '/$id/update'
      fullPath: '/strategic-report/device-update/$id/update'
      preLoaderRoute: typeof AuthStrategicReportDeviceUpdateIdUpdateRouteImport
      parentRoute: typeof AuthStrategicReportDeviceUpdateRouteRoute
    }
    '/_auth/strategic-report/development-index/$id/view': {
      id: '/_auth/strategic-report/development-index/$id/view'
      path: '/$id/view'
      fullPath: '/strategic-report/development-index/$id/view'
      preLoaderRoute: typeof AuthStrategicReportDevelopmentIndexIdViewRouteImport
      parentRoute: typeof AuthStrategicReportDevelopmentIndexRouteRoute
    }
    '/_auth/strategic-report/development-index/$id/update': {
      id: '/_auth/strategic-report/development-index/$id/update'
      path: '/$id/update'
      fullPath: '/strategic-report/development-index/$id/update'
      preLoaderRoute: typeof AuthStrategicReportDevelopmentIndexIdUpdateRouteImport
      parentRoute: typeof AuthStrategicReportDevelopmentIndexRouteRoute
    }
    '/_auth/data-summary/monthly-report/$id/view': {
      id: '/_auth/data-summary/monthly-report/$id/view'
      path: '/$id/view'
      fullPath: '/data-summary/monthly-report/$id/view'
      preLoaderRoute: typeof AuthDataSummaryMonthlyReportIdViewRouteImport
      parentRoute: typeof AuthDataSummaryMonthlyReportRouteRoute
    }
    '/_auth/data-summary/monthly-report/$id/update': {
      id: '/_auth/data-summary/monthly-report/$id/update'
      path: '/$id/update'
      fullPath: '/data-summary/monthly-report/$id/update'
      preLoaderRoute: typeof AuthDataSummaryMonthlyReportIdUpdateRouteImport
      parentRoute: typeof AuthDataSummaryMonthlyReportRouteRoute
    }
    '/_auth/data-summary/monthly-report/$id/report': {
      id: '/_auth/data-summary/monthly-report/$id/report'
      path: '/$id/report'
      fullPath: '/data-summary/monthly-report/$id/report'
      preLoaderRoute: typeof AuthDataSummaryMonthlyReportIdReportRouteImport
      parentRoute: typeof AuthDataSummaryMonthlyReportRouteRoute
    }
    '/_auth/basic-report/post-evaluation/$id/update': {
      id: '/_auth/basic-report/post-evaluation/$id/update'
      path: '/$id/update'
      fullPath: '/basic-report/post-evaluation/$id/update'
      preLoaderRoute: typeof AuthBasicReportPostEvaluationIdUpdateRouteImport
      parentRoute: typeof AuthBasicReportPostEvaluationRouteRoute
    }
    '/_auth/basic-report/fixed-assets/$id/update': {
      id: '/_auth/basic-report/fixed-assets/$id/update'
      path: '/$id/update'
      fullPath: '/basic-report/fixed-assets/$id/update'
      preLoaderRoute: typeof AuthBasicReportFixedAssetsIdUpdateRouteImport
      parentRoute: typeof AuthBasicReportFixedAssetsRouteRoute
    }
    '/_auth/basic-report/equity-projects/$id/update': {
      id: '/_auth/basic-report/equity-projects/$id/update'
      path: '/$id/update'
      fullPath: '/basic-report/equity-projects/$id/update'
      preLoaderRoute: typeof AuthBasicReportEquityProjectsIdUpdateRouteImport
      parentRoute: typeof AuthBasicReportEquityProjectsRouteRoute
    }
    '/_auth/data-summary/investment-plan/table/$tableName': {
      id: '/_auth/data-summary/investment-plan/table/$tableName'
      path: '/$tableName'
      fullPath: '/data-summary/investment-plan/table/$tableName'
      preLoaderRoute: typeof AuthDataSummaryInvestmentPlanTableTableNameRouteRouteImport
      parentRoute: typeof AuthDataSummaryInvestmentPlanTableRouteRoute
    }
    '/_auth/data-summary/completion-status/table/$tableName': {
      id: '/_auth/data-summary/completion-status/table/$tableName'
      path: '/$tableName'
      fullPath: '/data-summary/completion-status/table/$tableName'
      preLoaderRoute: typeof AuthDataSummaryCompletionStatusTableTableNameRouteRouteImport
      parentRoute: typeof AuthDataSummaryCompletionStatusTableRouteRoute
    }
  }
}

interface AuthBasicReportEquityProjectsRouteRouteChildren {
  AuthBasicReportEquityProjectsCreateRoute: typeof AuthBasicReportEquityProjectsCreateRoute
  AuthBasicReportEquityProjectsIndexRoute: typeof AuthBasicReportEquityProjectsIndexRoute
  AuthBasicReportEquityProjectsIdUpdateRoute: typeof AuthBasicReportEquityProjectsIdUpdateRoute
}

const AuthBasicReportEquityProjectsRouteRouteChildren: AuthBasicReportEquityProjectsRouteRouteChildren =
  {
    AuthBasicReportEquityProjectsCreateRoute:
      AuthBasicReportEquityProjectsCreateRoute,
    AuthBasicReportEquityProjectsIndexRoute:
      AuthBasicReportEquityProjectsIndexRoute,
    AuthBasicReportEquityProjectsIdUpdateRoute:
      AuthBasicReportEquityProjectsIdUpdateRoute,
  }

const AuthBasicReportEquityProjectsRouteRouteWithChildren =
  AuthBasicReportEquityProjectsRouteRoute._addFileChildren(
    AuthBasicReportEquityProjectsRouteRouteChildren,
  )

interface AuthBasicReportFixedAssetsRouteRouteChildren {
  AuthBasicReportFixedAssetsCreateRoute: typeof AuthBasicReportFixedAssetsCreateRoute
  AuthBasicReportFixedAssetsIndexRoute: typeof AuthBasicReportFixedAssetsIndexRoute
  AuthBasicReportFixedAssetsIdUpdateRoute: typeof AuthBasicReportFixedAssetsIdUpdateRoute
}

const AuthBasicReportFixedAssetsRouteRouteChildren: AuthBasicReportFixedAssetsRouteRouteChildren =
  {
    AuthBasicReportFixedAssetsCreateRoute:
      AuthBasicReportFixedAssetsCreateRoute,
    AuthBasicReportFixedAssetsIndexRoute: AuthBasicReportFixedAssetsIndexRoute,
    AuthBasicReportFixedAssetsIdUpdateRoute:
      AuthBasicReportFixedAssetsIdUpdateRoute,
  }

const AuthBasicReportFixedAssetsRouteRouteWithChildren =
  AuthBasicReportFixedAssetsRouteRoute._addFileChildren(
    AuthBasicReportFixedAssetsRouteRouteChildren,
  )

interface AuthBasicReportPostEvaluationRouteRouteChildren {
  AuthBasicReportPostEvaluationCreateRoute: typeof AuthBasicReportPostEvaluationCreateRoute
  AuthBasicReportPostEvaluationIndexRoute: typeof AuthBasicReportPostEvaluationIndexRoute
  AuthBasicReportPostEvaluationIdUpdateRoute: typeof AuthBasicReportPostEvaluationIdUpdateRoute
}

const AuthBasicReportPostEvaluationRouteRouteChildren: AuthBasicReportPostEvaluationRouteRouteChildren =
  {
    AuthBasicReportPostEvaluationCreateRoute:
      AuthBasicReportPostEvaluationCreateRoute,
    AuthBasicReportPostEvaluationIndexRoute:
      AuthBasicReportPostEvaluationIndexRoute,
    AuthBasicReportPostEvaluationIdUpdateRoute:
      AuthBasicReportPostEvaluationIdUpdateRoute,
  }

const AuthBasicReportPostEvaluationRouteRouteWithChildren =
  AuthBasicReportPostEvaluationRouteRoute._addFileChildren(
    AuthBasicReportPostEvaluationRouteRouteChildren,
  )

interface AuthBasicReportRouteRouteChildren {
  AuthBasicReportEquityProjectsRouteRoute: typeof AuthBasicReportEquityProjectsRouteRouteWithChildren
  AuthBasicReportFixedAssetsRouteRoute: typeof AuthBasicReportFixedAssetsRouteRouteWithChildren
  AuthBasicReportPostEvaluationRouteRoute: typeof AuthBasicReportPostEvaluationRouteRouteWithChildren
  AuthBasicReportIndexRoute: typeof AuthBasicReportIndexRoute
}

const AuthBasicReportRouteRouteChildren: AuthBasicReportRouteRouteChildren = {
  AuthBasicReportEquityProjectsRouteRoute:
    AuthBasicReportEquityProjectsRouteRouteWithChildren,
  AuthBasicReportFixedAssetsRouteRoute:
    AuthBasicReportFixedAssetsRouteRouteWithChildren,
  AuthBasicReportPostEvaluationRouteRoute:
    AuthBasicReportPostEvaluationRouteRouteWithChildren,
  AuthBasicReportIndexRoute: AuthBasicReportIndexRoute,
}

const AuthBasicReportRouteRouteWithChildren =
  AuthBasicReportRouteRoute._addFileChildren(AuthBasicReportRouteRouteChildren)

interface AuthDataSummaryCompletionStatusTableRouteRouteChildren {
  AuthDataSummaryCompletionStatusTableTableNameRouteRoute: typeof AuthDataSummaryCompletionStatusTableTableNameRouteRoute
  AuthDataSummaryCompletionStatusTableIndexRoute: typeof AuthDataSummaryCompletionStatusTableIndexRoute
}

const AuthDataSummaryCompletionStatusTableRouteRouteChildren: AuthDataSummaryCompletionStatusTableRouteRouteChildren =
  {
    AuthDataSummaryCompletionStatusTableTableNameRouteRoute:
      AuthDataSummaryCompletionStatusTableTableNameRouteRoute,
    AuthDataSummaryCompletionStatusTableIndexRoute:
      AuthDataSummaryCompletionStatusTableIndexRoute,
  }

const AuthDataSummaryCompletionStatusTableRouteRouteWithChildren =
  AuthDataSummaryCompletionStatusTableRouteRoute._addFileChildren(
    AuthDataSummaryCompletionStatusTableRouteRouteChildren,
  )

interface AuthDataSummaryCompletionStatusRouteRouteChildren {
  AuthDataSummaryCompletionStatusTableRouteRoute: typeof AuthDataSummaryCompletionStatusTableRouteRouteWithChildren
  AuthDataSummaryCompletionStatusIndexRoute: typeof AuthDataSummaryCompletionStatusIndexRoute
}

const AuthDataSummaryCompletionStatusRouteRouteChildren: AuthDataSummaryCompletionStatusRouteRouteChildren =
  {
    AuthDataSummaryCompletionStatusTableRouteRoute:
      AuthDataSummaryCompletionStatusTableRouteRouteWithChildren,
    AuthDataSummaryCompletionStatusIndexRoute:
      AuthDataSummaryCompletionStatusIndexRoute,
  }

const AuthDataSummaryCompletionStatusRouteRouteWithChildren =
  AuthDataSummaryCompletionStatusRouteRoute._addFileChildren(
    AuthDataSummaryCompletionStatusRouteRouteChildren,
  )

interface AuthDataSummaryInvestmentPlanTableRouteRouteChildren {
  AuthDataSummaryInvestmentPlanTableTableNameRouteRoute: typeof AuthDataSummaryInvestmentPlanTableTableNameRouteRoute
  AuthDataSummaryInvestmentPlanTableIndexRoute: typeof AuthDataSummaryInvestmentPlanTableIndexRoute
}

const AuthDataSummaryInvestmentPlanTableRouteRouteChildren: AuthDataSummaryInvestmentPlanTableRouteRouteChildren =
  {
    AuthDataSummaryInvestmentPlanTableTableNameRouteRoute:
      AuthDataSummaryInvestmentPlanTableTableNameRouteRoute,
    AuthDataSummaryInvestmentPlanTableIndexRoute:
      AuthDataSummaryInvestmentPlanTableIndexRoute,
  }

const AuthDataSummaryInvestmentPlanTableRouteRouteWithChildren =
  AuthDataSummaryInvestmentPlanTableRouteRoute._addFileChildren(
    AuthDataSummaryInvestmentPlanTableRouteRouteChildren,
  )

interface AuthDataSummaryInvestmentPlanRouteRouteChildren {
  AuthDataSummaryInvestmentPlanTableRouteRoute: typeof AuthDataSummaryInvestmentPlanTableRouteRouteWithChildren
  AuthDataSummaryInvestmentPlanIndexRoute: typeof AuthDataSummaryInvestmentPlanIndexRoute
}

const AuthDataSummaryInvestmentPlanRouteRouteChildren: AuthDataSummaryInvestmentPlanRouteRouteChildren =
  {
    AuthDataSummaryInvestmentPlanTableRouteRoute:
      AuthDataSummaryInvestmentPlanTableRouteRouteWithChildren,
    AuthDataSummaryInvestmentPlanIndexRoute:
      AuthDataSummaryInvestmentPlanIndexRoute,
  }

const AuthDataSummaryInvestmentPlanRouteRouteWithChildren =
  AuthDataSummaryInvestmentPlanRouteRoute._addFileChildren(
    AuthDataSummaryInvestmentPlanRouteRouteChildren,
  )

interface AuthDataSummaryMonthlyReportRouteRouteChildren {
  AuthDataSummaryMonthlyReportCreateRoute: typeof AuthDataSummaryMonthlyReportCreateRoute
  AuthDataSummaryMonthlyReportIndexRoute: typeof AuthDataSummaryMonthlyReportIndexRoute
  AuthDataSummaryMonthlyReportIdReportRoute: typeof AuthDataSummaryMonthlyReportIdReportRoute
  AuthDataSummaryMonthlyReportIdUpdateRoute: typeof AuthDataSummaryMonthlyReportIdUpdateRoute
  AuthDataSummaryMonthlyReportIdViewRoute: typeof AuthDataSummaryMonthlyReportIdViewRoute
}

const AuthDataSummaryMonthlyReportRouteRouteChildren: AuthDataSummaryMonthlyReportRouteRouteChildren =
  {
    AuthDataSummaryMonthlyReportCreateRoute:
      AuthDataSummaryMonthlyReportCreateRoute,
    AuthDataSummaryMonthlyReportIndexRoute:
      AuthDataSummaryMonthlyReportIndexRoute,
    AuthDataSummaryMonthlyReportIdReportRoute:
      AuthDataSummaryMonthlyReportIdReportRoute,
    AuthDataSummaryMonthlyReportIdUpdateRoute:
      AuthDataSummaryMonthlyReportIdUpdateRoute,
    AuthDataSummaryMonthlyReportIdViewRoute:
      AuthDataSummaryMonthlyReportIdViewRoute,
  }

const AuthDataSummaryMonthlyReportRouteRouteWithChildren =
  AuthDataSummaryMonthlyReportRouteRoute._addFileChildren(
    AuthDataSummaryMonthlyReportRouteRouteChildren,
  )

interface AuthDataSummaryRouteRouteChildren {
  AuthDataSummaryCompletionStatusRouteRoute: typeof AuthDataSummaryCompletionStatusRouteRouteWithChildren
  AuthDataSummaryInvestmentPlanRouteRoute: typeof AuthDataSummaryInvestmentPlanRouteRouteWithChildren
  AuthDataSummaryMonthlyReportRouteRoute: typeof AuthDataSummaryMonthlyReportRouteRouteWithChildren
  AuthDataSummaryIndexRoute: typeof AuthDataSummaryIndexRoute
}

const AuthDataSummaryRouteRouteChildren: AuthDataSummaryRouteRouteChildren = {
  AuthDataSummaryCompletionStatusRouteRoute:
    AuthDataSummaryCompletionStatusRouteRouteWithChildren,
  AuthDataSummaryInvestmentPlanRouteRoute:
    AuthDataSummaryInvestmentPlanRouteRouteWithChildren,
  AuthDataSummaryMonthlyReportRouteRoute:
    AuthDataSummaryMonthlyReportRouteRouteWithChildren,
  AuthDataSummaryIndexRoute: AuthDataSummaryIndexRoute,
}

const AuthDataSummaryRouteRouteWithChildren =
  AuthDataSummaryRouteRoute._addFileChildren(AuthDataSummaryRouteRouteChildren)

interface AuthStrategicReportDevelopmentIndexRouteRouteChildren {
  AuthStrategicReportDevelopmentIndexCreateRoute: typeof AuthStrategicReportDevelopmentIndexCreateRoute
  AuthStrategicReportDevelopmentIndexIndexRoute: typeof AuthStrategicReportDevelopmentIndexIndexRoute
  AuthStrategicReportDevelopmentIndexIdUpdateRoute: typeof AuthStrategicReportDevelopmentIndexIdUpdateRoute
  AuthStrategicReportDevelopmentIndexIdViewRoute: typeof AuthStrategicReportDevelopmentIndexIdViewRoute
}

const AuthStrategicReportDevelopmentIndexRouteRouteChildren: AuthStrategicReportDevelopmentIndexRouteRouteChildren =
  {
    AuthStrategicReportDevelopmentIndexCreateRoute:
      AuthStrategicReportDevelopmentIndexCreateRoute,
    AuthStrategicReportDevelopmentIndexIndexRoute:
      AuthStrategicReportDevelopmentIndexIndexRoute,
    AuthStrategicReportDevelopmentIndexIdUpdateRoute:
      AuthStrategicReportDevelopmentIndexIdUpdateRoute,
    AuthStrategicReportDevelopmentIndexIdViewRoute:
      AuthStrategicReportDevelopmentIndexIdViewRoute,
  }

const AuthStrategicReportDevelopmentIndexRouteRouteWithChildren =
  AuthStrategicReportDevelopmentIndexRouteRoute._addFileChildren(
    AuthStrategicReportDevelopmentIndexRouteRouteChildren,
  )

interface AuthStrategicReportDeviceUpdateRouteRouteChildren {
  AuthStrategicReportDeviceUpdateCreateRoute: typeof AuthStrategicReportDeviceUpdateCreateRoute
  AuthStrategicReportDeviceUpdateIndexRoute: typeof AuthStrategicReportDeviceUpdateIndexRoute
  AuthStrategicReportDeviceUpdateIdUpdateRoute: typeof AuthStrategicReportDeviceUpdateIdUpdateRoute
}

const AuthStrategicReportDeviceUpdateRouteRouteChildren: AuthStrategicReportDeviceUpdateRouteRouteChildren =
  {
    AuthStrategicReportDeviceUpdateCreateRoute:
      AuthStrategicReportDeviceUpdateCreateRoute,
    AuthStrategicReportDeviceUpdateIndexRoute:
      AuthStrategicReportDeviceUpdateIndexRoute,
    AuthStrategicReportDeviceUpdateIdUpdateRoute:
      AuthStrategicReportDeviceUpdateIdUpdateRoute,
  }

const AuthStrategicReportDeviceUpdateRouteRouteWithChildren =
  AuthStrategicReportDeviceUpdateRouteRoute._addFileChildren(
    AuthStrategicReportDeviceUpdateRouteRouteChildren,
  )

interface AuthStrategicReportRouteRouteChildren {
  AuthStrategicReportDevelopmentIndexRouteRoute: typeof AuthStrategicReportDevelopmentIndexRouteRouteWithChildren
  AuthStrategicReportDeviceUpdateRouteRoute: typeof AuthStrategicReportDeviceUpdateRouteRouteWithChildren
}

const AuthStrategicReportRouteRouteChildren: AuthStrategicReportRouteRouteChildren =
  {
    AuthStrategicReportDevelopmentIndexRouteRoute:
      AuthStrategicReportDevelopmentIndexRouteRouteWithChildren,
    AuthStrategicReportDeviceUpdateRouteRoute:
      AuthStrategicReportDeviceUpdateRouteRouteWithChildren,
  }

const AuthStrategicReportRouteRouteWithChildren =
  AuthStrategicReportRouteRoute._addFileChildren(
    AuthStrategicReportRouteRouteChildren,
  )

interface AuthRouteChildren {
  AuthBasicReportRouteRoute: typeof AuthBasicReportRouteRouteWithChildren
  AuthDataSummaryRouteRoute: typeof AuthDataSummaryRouteRouteWithChildren
  AuthStrategicReportRouteRoute: typeof AuthStrategicReportRouteRouteWithChildren
  AuthIndexRoute: typeof AuthIndexRoute
}

const AuthRouteChildren: AuthRouteChildren = {
  AuthBasicReportRouteRoute: AuthBasicReportRouteRouteWithChildren,
  AuthDataSummaryRouteRoute: AuthDataSummaryRouteRouteWithChildren,
  AuthStrategicReportRouteRoute: AuthStrategicReportRouteRouteWithChildren,
  AuthIndexRoute: AuthIndexRoute,
}

const AuthRouteWithChildren = AuthRoute._addFileChildren(AuthRouteChildren)

const rootRouteChildren: RootRouteChildren = {
  AuthRoute: AuthRouteWithChildren,
  AuthenticationCallbackRoute: AuthenticationCallbackRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
