import { useMutation, useQuery } from '@tanstack/react-query'
import {
  Button,
  DatePicker,
  Form,
  message,
  Modal,
  Table,
  type TableColumnsType,
} from 'antd'
import dayjs, { Dayjs } from 'dayjs'
import { useState } from 'react'

import { request, type APIResponse } from '@/lib/request'

export const ReportDeadline = ({ date }: { date: Dayjs }) => {
  const [visible, setVisible] = useState(false)

  const [form] = Form.useForm()

  const getDeadlineList = useQuery({
    queryKey: [dayjs(date).format('YYYY')],
    queryFn: async ({ queryKey: [year] }) => {
      const res = await request<
        APIResponse<{
          list: {
            id: string
            period: string
            latest_date: string
          }[]
        }>
      >('/aggregate/monthly-report/date-limit-list', {
        query: { year },
      })

      if (res.code === 200001) {
        return res.data.list
      }

      message.error(res?.message)
      return []
    },
    enabled: !!date,
  })

  const setDeadline = useMutation({
    mutationFn: async (data: { period: string; latest_date: string }[]) => {
      const res = await request<APIResponse<null>>(
        '/aggregate/monthly-report/date-limit-save',
        {
          method: 'POST',
          body: { data },
        },
      )
      if (res.code === 200001) {
        message.success(res.message)
        setVisible(false)
        return
      }
      message.error(res?.message)
    },
    onError: (err) => message.error(JSON.stringify(err)),
  })

  const columns: TableColumnsType<{
    id: string
    period: string
    latest_date: string
  }> = [
    {
      title: '填报周期',
      dataIndex: 'period',
      render: (text: string) => dayjs(text).format('YYYY-MM'),
    },
    {
      title: '上报截止日期',
      dataIndex: 'latest_date',
      render: (_, record) => (
        <Form.Item
          name={record.id}
          noStyle
          initialValue={dayjs(record.latest_date)}
          rules={[{ required: true, message: '请选择上报截止日期' }]}
        >
          <DatePicker className="w-full" required />
        </Form.Item>
      ),
    },
  ]

  return (
    <>
      <Modal
        title="设置上报截止时间"
        open={visible}
        onCancel={() => setVisible(false)}
        onOk={form.submit}
        okText="保存"
        cancelText="取消"
      >
        <Form
          form={form}
          onFinish={(values) => {
            const body =
              getDeadlineList.data?.map((item) => ({
                id: item.id,
                period: item.period,
                latest_date: values[item.id]
                  ?.endOf('day')
                  .format('YYYY-MM-DD HH:mm:ss'),
              })) ?? []

            setDeadline.mutate(body)
          }}
        >
          <Table
            columns={columns}
            loading={getDeadlineList.isFetching || setDeadline.isPending}
            dataSource={getDeadlineList.data}
            size="small"
            pagination={false}
            rowKey="id"
            bordered
          />
        </Form>
      </Modal>
      <Button onClick={() => setVisible(true)}>设置上报截止时间</Button>
    </>
  )
}
