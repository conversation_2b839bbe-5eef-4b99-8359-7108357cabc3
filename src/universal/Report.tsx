import { useMutation } from '@tanstack/react-query'
import { Modal, Button, Progress } from 'antd'
import { useState, useEffect, useRef } from 'react'

import { type APIResponse, request } from '@/lib/request.ts'

interface ReportModalProps {
  open: boolean // 弹窗是否可见
  onClose: () => void // 弹窗关闭回调
  apiUrl: string // 上报接口URL
  apiParams: Record<string, unknown> // 上报接口参数
  onSuccess?: () => void // 上报成功回调
  textOptions?: {
    title?: string // 弹窗标题
    success?: string // 成功提示
    error?: string // 失败提示
    loading?: string // 加载提示
  }
}

export const ReportModal = ({
  open,
  onClose,
  apiUrl,
  apiParams,
  onSuccess,
  textOptions = {
    title: '数据上报',
    success: '上报成功',
    error: '上报失败',
    loading: '上报中...',
  },
}: ReportModalProps) => {
  const progressInterval = useRef<NodeJS.Timeout | null>(null)
  const abortController = useRef<AbortController | null>(null)

  const [reportStatus, setReportStatus] = useState<
    'idle' | 'loading' | 'error' | 'success'
  >('idle')
  const [reportMessage, setReportMessage] = useState('')
  const [progress, setProgress] = useState(0)

  // 重置所有状态
  const resetState = () => {
    setReportStatus('idle')
    setReportMessage('')
    setProgress(0)
    if (progressInterval.current) {
      clearInterval(progressInterval.current)
      progressInterval.current = null
    }
    if (abortController.current) {
      abortController.current.abort() // 请求取消
      abortController.current = null
    }
  }

  // 关闭模态框处理
  const handleClose = () => {
    onClose()
    resetState()
  }

  // 模拟进度条加载效果（1秒内到99%）
  const startProgressSimulation = () => {
    setProgress(0)
    const startTime = Date.now()
    const duration = 1000
    const targetProgress = 99

    if (progressInterval.current) {
      clearInterval(progressInterval.current)
    }

    progressInterval.current = setInterval(() => {
      const elapsed = Date.now() - startTime
      const percentage = Math.min(
        (elapsed / duration) * targetProgress,
        targetProgress,
      )
      setProgress(Math.floor(percentage))

      if (percentage >= targetProgress) {
        clearInterval(progressInterval.current as NodeJS.Timeout)
      }
    }, 50)
  }

  const handleReport = useMutation({
    mutationKey: ['report'],
    mutationFn: async () => {
      abortController.current = new AbortController()
      const signal = abortController.current.signal

      const res = await request<APIResponse<null>>(apiUrl, {
        method: 'POST',
        body: apiParams,
        signal, // 使用abort信号
      })

      if (res.code !== 200001) {
        throw new Error(res.message || textOptions.error)
      }
      return res
    },
    onMutate: () => {
      setReportStatus('loading')
      startProgressSimulation()
    },
    onSuccess: () => {
      setProgress(100)
      setReportStatus('success')
    },
    onError: (err: Error) => {
      if (progressInterval.current) {
        clearInterval(progressInterval.current)
      }
      setReportStatus('error')
      setReportMessage(err.message)
    },
  })

  // 自动触发上报逻辑
  useEffect(() => {
    if (open && reportStatus === 'idle' && !handleReport.isPending) {
      handleReport.mutate()
    }
  }, [open, handleReport, reportStatus])

  // 关闭时清理
  useEffect(() => {
    if (!open) {
      resetState()
    }
  }, [reportStatus, open])

  return (
    <Modal
      title={textOptions.title}
      width={420}
      open={open}
      onCancel={handleClose}
      onOk={handleClose}
      footer={null}
      maskClosable={false}
      closable={reportStatus === 'error'}
    >
      <div style={{ textAlign: 'center', padding: '20px 0' }}>
        <Progress
          type="circle"
          percent={reportStatus === 'success' ? 100 : progress}
          size={120}
          status={
            reportStatus === 'error'
              ? 'exception'
              : reportStatus === 'success'
                ? 'success'
                : 'active'
          }
        />

        {reportStatus === 'loading' && (
          <h3 className="mt-4 text-xl">{textOptions.loading}</h3>
        )}

        {reportStatus === 'error' && (
          <>
            <h3 className="mt-4 text-xl">{textOptions.error}</h3>
            {reportMessage && (
              <p style={{ color: '#666', maxWidth: 300, margin: '10px auto' }}>
                {reportMessage}
              </p>
            )}
          </>
        )}

        {reportStatus === 'success' && (
          <h3 className="mt-4 text-xl">{textOptions.success}</h3>
        )}

        <div className="mt-6">
          {reportStatus === 'loading' ? (
            <Button
              onClick={() => {
                handleClose()
              }}
            >
              取消
            </Button>
          ) : reportStatus === 'error' ? (
            <Button onClick={handleClose} type="primary">
              确定
            </Button>
          ) : reportStatus === 'success' ? (
            <Button
              onClick={() => {
                handleClose()
                onSuccess?.()
              }}
              type="primary"
            >
              确定
            </Button>
          ) : null}
        </div>
      </div>
    </Modal>
  )
}
