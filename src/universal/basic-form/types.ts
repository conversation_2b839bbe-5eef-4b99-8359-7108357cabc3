type AutoGenerated = {
  approval_id: string
  approval_node_id: string
  approval_node_status: number
  approval_status: number
  approval_update_at: string
  creator_id: string
  creator_name: string
  delete_view: number
  lock_data: number
  modify_id: string
  modify_name: string
  next_node_id: string
  op_name: string
  op_uid: string
  prev_node_id: string
  project_id: string
  project_op_company_id: string
  project_op_name: string
  project_op_uid: string
  project_status_aborted_view: number
  project_status_completed_view: number
  project_status_in_progress_view: number
  project_status: number
  reject_reason: string
  reject_view: number
  reprot_month: string
  version_id: string
}

export type EquityProjectForm = {
  company_id: string
  company_name: string
  complete_time_expect: string
  completed_fund_source_loan: number
  completed_fund_source_other: number
  completed_fund_source_self: number
  completed_remarks: string
  equity_investment_type: number
  equity_ratio_after_investmen: number
  fund_source_loan: number
  fund_source_other: number
  fund_source_self: number
  industry_new_type: string
  industry_type: string
  investment_completed_cur_year: number
  investment_necessity: string
  investment_return_rate_expect: number
  investment_year: string
  investmentcompleted_last_year: number
  investor_type: number
  is_plan_investment: number
  plan_investment_cur_year: number
  project_area: string
  project_category: string
  project_content: string
  project_expect: string
  project_name: string
  project_plan_total_investment: number
  project_progress_desc: string
  remarks: string
  shareholders_info: string
  special_consideration_type: number
  special_total_investment: number
  start_time: string
}
export type EquityProjectDTO = EquityProjectForm & AutoGenerated

export type FixedAssetForm = {
  company_id: string
  company_name: string
  investor_type: number
  project_name: string
  project_area: string
  project_content: string
  project_expect: string
  industry_type: string
  is_plan_investment: number
  is_real_estate_investment: number
  project_plan_total_investment: number
  investmentcompleted_last_year: number
  plan_investment_cur_year: number
  investment_return_rate_expect: number
  has_equipment_upgrade: number
  equipment_upgrade_amount: number
  is_key_project: number
  project_phase: number
  special_consideration_type: number
  special_total_investment: number
  investment_necessity: string
  fund_source_self: number
  fund_source_loan: number
  fund_source_other: number
  remarks: string
  project_progress_desc: string
  investment_completed_cur_year: number
  completed_fund_source_self: number
  completed_fund_source_loan: number
  completed_fund_source_other: number
  funds_received: number
  completed_remarks: string
  actual_start_date: string
  complete_time_expect: string
  industry_new_type: string
  investment_year: string
  project_category: string
  start_time: string
}
export type FixedAssetDTO = FixedAssetForm & AutoGenerated

export type PostEvaluationForm = {
  company_id: string
  company_name: string
  files: []
  project_style: number
  project_name: string
  region: number
  project_area: string
  is_major: number
  industry_type: string
  project_total_investment: number
  organization_type: number
  eval_type: number
  remarks: string
  performance: string
  complete_time_expect: string
  industry_new_type: string
  investment_year: string
  project_category: string
  start_time: string
}
export type PostEvaluationDTO = PostEvaluationForm & AutoGenerated
