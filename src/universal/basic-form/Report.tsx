import { useMutation } from '@tanstack/react-query'
import { But<PERSON>, Col, DatePicker, message, Modal, Row, Spin } from 'antd'
import dayjs from 'dayjs'
import { useState } from 'react'

import { type APIResponse, request } from '@/lib/request'

export const ReportModal = ({
  reportUrl,
  disabled,
}: {
  reportUrl: string
  disabled: boolean
}) => {
  const [reportModalOpen, setReportModalOpen] = useState(false)

  const [selectedMonth, setSelectedMonth] = useState<dayjs.Dayjs | null>(null)

  const handleReport = useMutation({
    mutationKey: [reportUrl],
    mutationFn: async () => {
      if (!selectedMonth) {
        message.error('请选择上报月份')
        return
      }

      const res = await request<APIResponse<'ok'>>(reportUrl, {
        method: 'POST',
        body: {
          report_month: dayjs(selectedMonth)
            .startOf('month')
            .format('YYYY-MM-DD HH:mm:ss'),
        },
      })
      return res
    },
    onSuccess: (res) => {
      if (res?.code === 200001) {
        message.success('操作成功')
        setReportModalOpen(false)
        window.location.reload()
        return
      }
      message.error(res?.message)
    },
    onError: (err) => message.error(JSON.stringify(err)),
  })

  return (
    <>
      <Modal
        title="上报所有数据"
        open={reportModalOpen}
        onCancel={() => setReportModalOpen(false)}
        onOk={() => handleReport.mutate()}
      >
        <Spin spinning={handleReport.isPending}>
          <Row>
            <Col span={8}>请选择上报月份</Col>
            <Col span={16}>
              <DatePicker
                className="w-full"
                picker="month"
                value={selectedMonth}
                onChange={(value) => setSelectedMonth(value)}
              />
            </Col>
          </Row>
        </Spin>
      </Modal>

      <Button disabled={disabled} onClick={() => setReportModalOpen(true)}>
        上报所有数据
      </Button>
    </>
  )
}
