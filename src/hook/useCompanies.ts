import { useQuery } from '@tanstack/react-query'

import { type APIResponse, request } from '@/lib/request'

export interface Company {
  id: string
  name: string
  code?: string
}

// 获取填报单位列表
async function fetchCompanies(): Promise<Company[]> {
  const response = await request<APIResponse<Company[]>>('/orgs/tree', {
    method: 'GET',
  })

  if (response.code !== 200001) {
    throw new Error(response.message || '获取填报单位失败')
  }

  return response.data || []
}

// 统一的填报单位Hook
export function useCompanies() {
  return useQuery({
    queryKey: ['companies'],
    queryFn: fetchCompanies,
    staleTime: 5 * 60 * 1000, // 5分钟缓存
  })
}

// 获取填报单位选项（用于Select组件）
export function useCompanyOptions() {
  const { data: options, ...rest } = useCompanies()

  return {
    options,
    ...rest,
  }
}
