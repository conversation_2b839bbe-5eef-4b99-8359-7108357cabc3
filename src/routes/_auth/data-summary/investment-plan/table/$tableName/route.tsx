import { createFileRoute } from '@tanstack/react-router'

import { InvestmentPlanTableNamesMap } from '@/pages/investment-plan/constants'
import { TableUpdate } from '@/pages/investment-plan/Update'

export const Route = createFileRoute(
  '/_auth/data-summary/investment-plan/table/$tableName',
)({
  component: TableUpdate,
  loader: ({ params }) => {
    const { tableName } = params as { tableName: string }
    const name =
      InvestmentPlanTableNamesMap[
        tableName as keyof typeof InvestmentPlanTableNamesMap
      ]

    return {
      name,
    }
  },
})
