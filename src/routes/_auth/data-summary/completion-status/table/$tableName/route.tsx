import { createFileRoute } from '@tanstack/react-router'

import { CompletionStatusTableNamesMap } from '@/pages/completion-status/constants'
import { TableUpdate } from '@/pages/completion-status/Update'

export const Route = createFileRoute(
  '/_auth/data-summary/completion-status/table/$tableName',
)({
  component: TableUpdate,
  loader: ({ params }) => {
    const { tableName } = params as { tableName: string }
    const name =
      CompletionStatusTableNamesMap[
        tableName as keyof typeof CompletionStatusTableNamesMap
      ]

    return {
      name,
    }
  },
})
