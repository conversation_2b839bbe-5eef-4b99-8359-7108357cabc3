{"name": "poly-zttb-cms", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build:sit": "tsc -b && vite build --mode sit", "lint": "eslint .", "preview": "vite preview", "lint:fix": "eslint . --fix", "prettier": "prettier --write */**/*.{js,jsx,json,ts,tsx,scss,css,md}", "preinstall": "npx only-allow pnpm", "prepare": "husky"}, "dependencies": {"@ant-design/icons": "~5.6.1", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@tanstack/react-router": "^1.130.2", "@tanstack/react-router-devtools": "^1.130.2", "antd": "^5.26.6", "antd-style": "^3.7.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "framer-motion": "^12.23.11", "lucide-react": "^0.533.0", "numeral": "^2.0.6", "nuqs": "^2.4.3", "ofetch": "^1.4.1", "qs": "^6.14.0", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11"}, "devDependencies": {"@eslint/js": "^9.30.1", "@tanstack/router-plugin": "^1.130.2", "@types/node": "^24.1.0", "@types/numeral": "^2.0.5", "@types/qs": "^6.14.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.8", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-import": "^2.32.0", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "only-allow": "^1.2.1", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tw-animate-css": "^1.3.6", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}, "lint-staged": {"*/**/*.{js,jsx,ts,tsx}": ["prettier --write", "eslint --fix", "eslint"], "*/**/*.{json,css,md}": ["prettier --write"]}}