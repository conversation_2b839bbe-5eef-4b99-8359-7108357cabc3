# Solution: Properly Disabling Ant Design Form Fields

## Problem

The original code attempted to disable form fields using `form.setFields()` with a `disabled` property:

```javascript
form.setFields(
  disabledFields.map((item) => ({
    name: item,
    disabled: true,
  })),
)
```

**This approach does NOT work** because `form.setFields()` is used for setting field values and validation states, not for disabling form controls.

## Correct Solution

### 1. Create a helper function to check if fields should be disabled

```typescript
// Fields that should be disabled when lock_data === 1
const DISABLED_FIELDS_WHEN_LOCKED = [
  'company_id',
  'company_name',
  'investor_type',
  'project_name',
  'project_area',
  // ... all other field names
]

export function FixedAssetForm({ isUpdate }: Readonly<{ isUpdate?: boolean }>) {
  const [lockData, setLockData] = useState<boolean>(false)

  // Helper function to check if a field should be disabled
  const isFieldDisabled = useCallback((fieldName: string) => {
    return lockData && DISABLED_FIELDS_WHEN_LOCKED.includes(fieldName)
  }, [lockData])

  // ... rest of component
}
```

### 2. Apply the `disabled` prop directly to each form component

For **Input** components:

```jsx
<Form.Item name="project_name" label="项目名称">
  <Input
    placeholder="请输入项目名称"
    disabled={isFieldDisabled('project_name')}
  />
</Form.Item>
```

For **Select** components:

```jsx
<Form.Item name="investor_type" label="投资分类">
  <Select
    placeholder="请选择投资分类"
    options={INVESTOR_TYPE_OPTIONS}
    disabled={isFieldDisabled('investor_type')}
  />
</Form.Item>
```

For **DatePicker** components:

```jsx
<Form.Item name="investment_year" label="填报年份">
  <DatePicker
    picker="year"
    placeholder="请选择填报年份"
    disabled={isFieldDisabled('investment_year')}
  />
</Form.Item>
```

For **Radio** components:

```jsx
<Form.Item name="is_plan_investment" label="是否计划投资">
  <Radio.Group disabled={isFieldDisabled('is_plan_investment')}>
    <Radio value={1}>是</Radio>
    <Radio value={0}>否</Radio>
  </Radio.Group>
</Form.Item>
```

For **Cascader** components:

```jsx
<Form.Item name="industry_new_type" label="所属战新产业">
  <Cascader
    placeholder="请选择所属战新产业"
    options={INDUSTRY_NEW_TYPE}
    disabled={isFieldDisabled('industry_new_type')}
  />
</Form.Item>
```

### 3. Pattern for all form components

The key principle is: **Add `disabled={isFieldDisabled('field_name')}` to every form control component** that should be disabled when `lock_data === 1`.

## Implementation Steps

1. ✅ Create the `DISABLED_FIELDS_WHEN_LOCKED` constant array
2. ✅ Create the `isFieldDisabled` helper function
3. ✅ Add `disabled` prop to each form component (Input, Select, DatePicker, Radio.Group, Cascader, etc.)

## Benefits of This Approach

1. **Actually works** - Form fields become truly disabled and uneditable
2. **Maintainable** - Easy to add/remove fields from the disabled list
3. **Type-safe** - TypeScript will catch any typos in field names
4. **Performance** - Uses `useCallback` to prevent unnecessary re-renders
5. **Consistent** - Same pattern applies to all Ant Design form components

## Implementation Status

✅ **Completed:**

- `investment_year` (DatePicker)
- `company_id` (Select)
- `investor_type` (Select)
- `project_name` (Input)
- `project_area` (Select - both domestic and overseas variants)

## Remaining Form Components to Update

You need to add `disabled={isFieldDisabled('field_name')}` to these remaining components:

### Select Components

```jsx
// project_category
<Select disabled={isFieldDisabled('project_category')} ... />

// industry_type
<Select disabled={isFieldDisabled('industry_type')} ... />

// has_equipment_upgrade
<Select disabled={isFieldDisabled('has_equipment_upgrade')} ... />

// special_consideration_type
<Select disabled={isFieldDisabled('special_consideration_type')} ... />
```

### Input Components

```jsx
// project_content
<Input.TextArea disabled={isFieldDisabled('project_content')} ... />

// project_expect
<Input.TextArea disabled={isFieldDisabled('project_expect')} ... />

// project_plan_total_investment
<Input disabled={isFieldDisabled('project_plan_total_investment')} ... />

// investmentcompleted_last_year
<Input disabled={isFieldDisabled('investmentcompleted_last_year')} ... />

// plan_investment_cur_year
<Input disabled={isFieldDisabled('plan_investment_cur_year')} ... />

// investment_return_rate_expect
<Input disabled={isFieldDisabled('investment_return_rate_expect')} ... />

// equipment_upgrade_amount
<Input disabled={isFieldDisabled('equipment_upgrade_amount')} ... />

// special_total_investment
<Input disabled={isFieldDisabled('special_total_investment')} ... />

// investment_necessity
<Input.TextArea disabled={isFieldDisabled('investment_necessity')} ... />

// fund_source_self
<Input disabled={isFieldDisabled('fund_source_self')} ... />

// fund_source_loan
<Input disabled={isFieldDisabled('fund_source_loan')} ... />

// fund_source_other
<Input disabled={isFieldDisabled('fund_source_other')} ... />

// remarks
<Input.TextArea disabled={isFieldDisabled('remarks')} ... />

// project_progress_desc
<Input.TextArea disabled={isFieldDisabled('project_progress_desc')} ... />

// investment_completed_cur_year
<Input disabled={isFieldDisabled('investment_completed_cur_year')} ... />

// completed_fund_source_self
<Input disabled={isFieldDisabled('completed_fund_source_self')} ... />

// completed_fund_source_loan
<Input disabled={isFieldDisabled('completed_fund_source_loan')} ... />

// completed_fund_source_other
<Input disabled={isFieldDisabled('completed_fund_source_other')} ... />

// funds_received
<Input disabled={isFieldDisabled('funds_received')} ... />

// completed_remarks
<Input.TextArea disabled={isFieldDisabled('completed_remarks')} ... />
```

### DatePicker Components

```jsx
// start_time
<DatePicker disabled={isFieldDisabled('start_time')} ... />

// complete_time_expect
<DatePicker disabled={isFieldDisabled('complete_time_expect')} ... />

// actual_start_date
<DatePicker disabled={isFieldDisabled('actual_start_date')} ... />
```

### Radio.Group Components

```jsx
// is_plan_investment
<Radio.Group disabled={isFieldDisabled('is_plan_investment')}>

// is_real_estate_investment
<Radio.Group disabled={isFieldDisabled('is_real_estate_investment')}>

// is_key_project
<Radio.Group disabled={isFieldDisabled('is_key_project')}>

// project_phase
<Radio.Group disabled={isFieldDisabled('project_phase')}>
```

### Cascader Components

```jsx
// industry_new_type
<Cascader disabled={isFieldDisabled('industry_new_type')} ... />
```

## Quick Search & Replace Guide

You can use your IDE's search and replace functionality to speed this up:

1. **Search for:** `<Input className="w-full"`
   **Replace with:** `<Input className="w-full" disabled={isFieldDisabled('FIELD_NAME')}`

2. **Search for:** `<Input.TextArea`
   **Replace with:** `<Input.TextArea disabled={isFieldDisabled('FIELD_NAME')}`

3. **Search for:** `<Select className="w-full"`
   **Replace with:** `<Select className="w-full" disabled={isFieldDisabled('FIELD_NAME')}`

4. **Search for:** `<DatePicker`
   **Replace with:** `<DatePicker disabled={isFieldDisabled('FIELD_NAME')}`

5. **Search for:** `<Radio.Group>`
   **Replace with:** `<Radio.Group disabled={isFieldDisabled('FIELD_NAME')}>`

6. **Search for:** `<Cascader`
   **Replace with:** `<Cascader disabled={isFieldDisabled('FIELD_NAME')}`

**Important:** Replace `FIELD_NAME` with the actual field name from the Form.Item's `name` prop above each component.

## Testing

After implementing all the disabled props:

1. Set `lock_data = 1` in your test data
2. Verify that all form fields become disabled and uneditable
3. Set `lock_data = 0` and verify fields become editable again
